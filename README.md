# MediaNexus

MediaNexus 是一个基于 Go 语言开发的 GB/T 28181 协议网关服务，用于连接上层业务平台与下层国标视频平台。这个名称表明该网关是连接各种媒体流的中心枢纽。
对于GB/T 28181来说，MediaNexus作为国标视频平台（如海康安防平台）的上级，国标平台作为摄像头的上级，

## 功能特性

- **协议转换**: 提供简单的 HTTP RESTful API，屏蔽底层 GB/T 28181 SIP 协议复杂性
- **设备管理**: 支持设备列表查询和状态管理
- **视频流控制**: 支持视频流点播请求，自动发送 SIP INVITE 消息
- **云台控制 (PTZ)**: 支持摄像头云台的方向控制和变焦操作，发送 DeviceControl MESSAGE
- **SIP信令处理**: 完整的 SIP INVITE 和 MESSAGE 请求构造，包含标准 SDP 和 XML 内容
- **无状态设计**: 基于内存的状态管理，支持快速部署
- **轻量级**: 只处理信令，不处理视频流，确保高性能

## 系统架构

```mermaid
graph LR
    A[云视后台] -- HTTP API --> B[MediaNexus]
    B -- SIP/GB28181 --> C[海康安防平台]
    C[海康安防平台] -- GB28181 --> E[摄像头]
    E -- RTSP --> D[解码终端]
    D[解码终端] -- HTTP API  --> B
```

## 快速开始

### 一键启动（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd media-nexus

# 2. 生成 API 文档并启动服务器
make docs
```

启动后访问 <http://localhost:8080/swagger/index.html> 查看完整的 API 文档。

### 编译

```bash
# 使用 Go 命令编译
go build -o media-nexus ./cmd/media-nexus

# 或使用 Makefile（推荐）
make build
```

### 配置

编辑 `config.yaml` 文件：

```yaml
server:
  http_port: 8080       # HTTP服务端口
  sip_ip: "0.0.0.0"     # SIP监听IP
  sip_port: 5060        # SIP监听端口
  sip_id: "44010000002000000099" # 网关国标ID
  sip_domain: "4401000000"       # 网关域

log:
  level: "debug"        # 日志级别: debug(启用API文档), info, warn, error
  path: "/var/log/media-nexus.log"
```

**重要**:

- 设置 `log.level: "debug"` 可启用 Swagger API 文档
- 生产环境建议使用 `"info"` 或更高级别以禁用 API 文档

### 运行

```bash
# 直接运行编译后的程序
./media-nexus

# 或使用 Makefile 编译并运行
make run

# 生成 Swagger 文档并启动服务器
make docs
```

## API 文档

### Swagger UI (推荐)

**⚠️ 安全提示**: Swagger UI 仅在 **debug 模式** 下可访问，生产环境会自动禁用以确保安全性。

启用 debug 模式并启动服务器后，可以通过 **Swagger UI** 查看和测试所有 API：

- **访问地址**: <http://localhost:8080/swagger/index.html>
- **启用条件**: 配置文件中 `log.level` 设置为 `"debug"`
- **功能特性**:
  - 📖 完整的 API 文档，包含中文描述
  - 🧪 交互式测试界面，可直接发送请求
  - 📋 详细的参数说明和示例数据
  - 🏷️ API 按功能分组（设备管理、视频流控制、设备控制等）
  - ⚠️ 完整的错误码说明

### 其他格式

- **JSON 格式**: <http://localhost:8080/swagger/doc.json>
- **YAML 格式**: <http://localhost:8080/swagger/doc.yaml>

### 更新文档

当 API 发生变更时，运行以下命令重新生成文档：

```bash
# 使用 Makefile（推荐）
make swagger-gen

# 或手动执行
# 1. 安装 swag 工具（如果尚未安装）
go install github.com/swaggo/swag/cmd/swag@latest
# 2. 生成文档
swag init -g internal/http/server.go -o docs
```

## 项目结构

```text
media-nexus/
├── cmd/
│   └── media-nexus/          # 主程序入口
├── docs/                    # Swagger API 文档
│   ├── docs.go              # Go 格式文档
│   ├── swagger.json         # JSON 格式文档
│   ├── swagger.yaml         # YAML 格式文档
│   └── README.md            # 文档使用说明
├── internal/
│   ├── config/              # 配置管理
│   ├── core/                # 核心业务逻辑
│   ├── http/                # HTTP服务器
│   ├── sip/                 # SIP服务器
│   └── state/               # 状态管理
├── pkg/
│   └── models/              # 数据模型
├── config.yaml              # 配置文件
└── README.md
```

## 快速测试

项目包含完整的测试脚本：

```bash
# 运行完整测试
./test.sh

```

测试脚本会自动验证：

- 服务器启动状态
- 设备列表API
- 视频流请求API
- PTZ控制API
- SIP INVITE和MESSAGE消息发送
- Swagger UI 安全访问控制

## 配置管理

MediaNexus 提供了Web界面的配置管理功能，支持在线查看和修改配置参数。

### 访问配置管理界面

**访问地址**: `http://<宿主机IP>:8081/config`

- **默认端口**: 8081
- **访问路径**: `/config`
- **示例**: `http://*************:8081/config`

### 首次启动配置

**重要**: 首次启动容器后，必须将配置中的IP地址修改为宿主机IP，否则SIP通信可能无法正常工作。

1. 访问配置管理界面：`http://<宿主机IP>:8081/config`
2. 修改 **SIP IP** 为宿主机IP地址
3. 点击 **保存配置** 按钮
4. **重启容器** 使配置生效

### 配置参数说明

#### 服务器配置 (Server)

- **HTTP端口** (`http_port`): HTTP API服务端口，默认8080
- **SIP IP** (`sip_ip`): SIP服务监听IP地址，**必须设置为宿主机IP**
- **SIP端口** (`sip_port`): SIP服务监听端口，默认5066
- **SIP ID** (`sip_id`): 网关国标ID，20位数字编码
- **SIP域** (`sip_domain`): 网关域编码，通常为区域编码
- **设备查询间隔** (`catalog_query_interval`): 设备目录查询间隔（秒），默认60
- **设备过期超时** (`device_expire_timeout`): 设备离线超时时间（秒），默认300

#### 日志配置 (Log)

- **日志级别** (`level`): 日志输出级别
  - `debug`: 调试模式，启用Swagger API文档
  - `info`: 信息模式，生产环境推荐
  - `warn`: 警告模式
  - `error`: 错误模式
- **日志路径** (`path`): 日志文件保存路径

#### Web管理配置 (WebAdmin)

- **启用状态** (`enabled`): 是否启用Web管理界面
- **访问端口** (`port`): Web管理界面端口，默认8081
- **访问路径** (`path`): Web管理界面路径，默认`/config`

### 配置管理操作

#### 查看当前配置

1. 访问配置管理界面
2. 页面会显示当前所有配置参数
3. 可以查看配置文件路径和最后修改时间

#### 修改配置

1. 在配置管理界面中修改需要的参数
2. 点击 **保存配置** 按钮
3. 系统会验证配置有效性
4. 保存成功后会提示需要重启容器

#### 应用配置变更

**重要提示**: 配置保存后必须重启容器才能生效

```bash
# Docker容器重启
docker restart <容器名称>

# 或使用docker-compose
docker-compose restart

# 或停止后重新启动
docker stop <容器名称>
docker start <容器名称>
```

### 配置安全说明

1. **线程安全**: 配置管理使用互斥锁保证多线程安全访问
2. **数据校验**: 保存前会验证配置参数的有效性
3. **备份机制**: 建议在修改前备份原始配置文件
4. **访问控制**: Web管理界面建议在内网环境使用

### 故障排除

#### 无法访问配置界面

- 检查webadmin.enabled是否为true
- 确认端口8081未被占用
- 检查防火墙设置

#### SIP通信异常

- 确认sip_ip设置为正确的宿主机IP
- 检查SIP端口是否被占用
- 验证SIP ID和域配置是否正确

#### 配置保存失败

- 检查配置文件写入权限
- 验证JSON格式是否正确
- 查看错误日志获取详细信息

## 注意事项

1. ✅ **SIP INVITE 已完整实现**：基于 panjjo/gosip 库，支持真实的 SIP 消息发送
2. ✅ **PTZ 控制已完整实现**：支持标准的云台控制命令，通过 SIP MESSAGE 发送 DeviceControl 指令
3. 🔒 **API 文档安全**：Swagger UI 仅在 debug 模式下启用，生产环境自动禁用
4. ⚠️ **配置管理**: 修改配置后必须重启容器才能生效
5. 🌐 **IP配置**: 首次启动后务必将SIP IP设置为宿主机IP地址

## 技术栈

本项目基于以下技术栈：

- **Web框架**: Gin
- **SIP协议栈**: panjjo/gosip
- **配置管理**: Viper
- **日志**: slog
- **API文档**: Swagger/OpenAPI 3.0
  - `github.com/swaggo/swag` - 文档生成工具
  - `github.com/swaggo/gin-swagger` - Gin 中间件
  - `github.com/swaggo/files` - 静态文件服务
