// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/control/ptz": {
            "post": {
                "description": "控制指定设备的云台运动，支持上下左右移动、缩放和停止",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "control"
                ],
                "summary": "云台控制",
                "parameters": [
                    {
                        "description": "云台控制参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PTZRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功发送云台控制命令",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误或无效的云台命令",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误或设备未找到",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    }
                }
            }
        },
        "/devices": {
            "get": {
                "description": "根据平台ID获取该平台下的所有真实摄像头设备信息（通过递归目录查询，过滤掉行政区划、业务分组等虚拟节点），支持分页查询",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "devices"
                ],
                "summary": "获取摄像头设备列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "平台ID，不传则获取所有平台的摄像头设备",
                        "name": "platform_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码，从1开始，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页大小，默认为20，最大为100",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功返回摄像头设备列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.DeviceListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "检查服务器运行状态",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "健康检查",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.HealthResponse"
                        }
                    }
                }
            }
        },
        "/stream/request": {
            "post": {
                "description": "向指定设备请求视频流，返回SSRC和会话ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "stream"
                ],
                "summary": "请求视频流",
                "parameters": [
                    {
                        "description": "视频流请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.StreamRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功发起视频流请求",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.StreamResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误或设备未找到",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    }
                }
            }
        },
        "/stream/stop": {
            "post": {
                "description": "停止指定会话的视频流",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "stream"
                ],
                "summary": "停止视频流",
                "parameters": [
                    {
                        "description": "停止视频流请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.StopRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功停止视频流",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误或会话未找到",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    }
                }
            }
        },
        "/stream/update": {
            "post": {
                "description": "由解码终端调用，更新指定会话的最后使用时间，用于跟踪流的活跃状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "stream"
                ],
                "summary": "更新会话使用时间",
                "parameters": [
                    {
                        "description": "更新会话使用时间请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UpdateSessionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功更新会话使用时间",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "404": {
                        "description": "会话未找到",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/models.APIResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.APIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "错误代码",
                    "type": "integer"
                },
                "data": {
                    "description": "响应数据"
                },
                "message": {
                    "description": "错误消息",
                    "type": "string"
                }
            }
        },
        "models.Device": {
            "type": "object",
            "properties": {
                "address": {
                    "description": "设备安装地址",
                    "type": "string"
                },
                "block": {
                    "description": "组织机构代码",
                    "type": "string"
                },
                "business_group_id": {
                    "description": "业务分组ID",
                    "type": "string"
                },
                "civil_code": {
                    "description": "行政区划代码",
                    "type": "string"
                },
                "device_type": {
                    "description": "新增字段用于支持递归目录查询",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.DeviceType"
                        }
                    ]
                },
                "gb_id": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "is_real_device": {
                    "description": "是否为真实设备（摄像头）",
                    "type": "boolean"
                },
                "last_updated": {
                    "description": "设备最后更新时间",
                    "type": "string"
                },
                "manufacturer": {
                    "description": "设备厂商",
                    "type": "string"
                },
                "model": {
                    "description": "设备型号",
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "owner": {
                    "description": "设备归属",
                    "type": "string"
                },
                "parent_id": {
                    "description": "父节点ID",
                    "type": "string"
                },
                "parental": {
                    "description": "是否有子设备",
                    "type": "integer"
                },
                "platform_id": {
                    "type": "string"
                },
                "register_way": {
                    "description": "注册方式",
                    "type": "string"
                },
                "secrecy": {
                    "description": "保密属性",
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "models.DeviceListResponse": {
            "type": "object",
            "properties": {
                "devices": {
                    "description": "设备列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Device"
                    }
                },
                "pagination": {
                    "description": "分页信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.PaginationResponse"
                        }
                    ]
                }
            }
        },
        "models.DeviceType": {
            "type": "string",
            "enum": [
                "administrative_region",
                "camera",
                "business_group",
                "virtual_organization",
                "system_directory",
                "unknown"
            ],
            "x-enum-comments": {
                "DeviceTypeAdministrativeRegion": "行政区划 (2-8位)",
                "DeviceTypeBusinessGroup": "业务分组 (20位,类型215)",
                "DeviceTypeCamera": "摄像头 (20位,类型131/132)",
                "DeviceTypeSystemDirectory": "系统目录 (20位,类型200)",
                "DeviceTypeUnknown": "未知类型",
                "DeviceTypeVirtualOrganization": "虚拟组织 (20位,类型216)"
            },
            "x-enum-descriptions": [
                "行政区划 (2-8位)",
                "摄像头 (20位,类型131/132)",
                "业务分组 (20位,类型215)",
                "虚拟组织 (20位,类型216)",
                "系统目录 (20位,类型200)",
                "未知类型"
            ],
            "x-enum-varnames": [
                "DeviceTypeAdministrativeRegion",
                "DeviceTypeCamera",
                "DeviceTypeBusinessGroup",
                "DeviceTypeVirtualOrganization",
                "DeviceTypeSystemDirectory",
                "DeviceTypeUnknown"
            ]
        },
        "models.HealthResponse": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "健康状态",
                    "type": "string"
                },
                "timestamp": {
                    "description": "时间戳",
                    "type": "integer"
                }
            }
        },
        "models.PTZRequest": {
            "type": "object",
            "required": [
                "command",
                "gb_id"
            ],
            "properties": {
                "command": {
                    "description": "云台控制命令",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.PtzCmd"
                        }
                    ]
                },
                "gb_id": {
                    "description": "GB/T 28181 设备 ID",
                    "type": "string"
                },
                "speed": {
                    "description": "云台控制速度",
                    "type": "integer",
                    "maximum": 255,
                    "minimum": 0
                }
            }
        },
        "models.PaginationResponse": {
            "type": "object",
            "properties": {
                "page": {
                    "description": "当前页码",
                    "type": "integer"
                },
                "page_size": {
                    "description": "每页大小",
                    "type": "integer"
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer"
                },
                "total_pages": {
                    "description": "总页数",
                    "type": "integer"
                }
            }
        },
        "models.PtzCmd": {
            "type": "string",
            "enum": [
                "up",
                "down",
                "left",
                "right",
                "zoom_in",
                "zoom_out",
                "stop"
            ],
            "x-enum-comments": {
                "PtzCmd__Down": "向下移动",
                "PtzCmd__Left": "向左移动",
                "PtzCmd__Right": "向右移动",
                "PtzCmd__Stop": "停止",
                "PtzCmd__Up": "向上移动",
                "PtzCmd__ZoomIn": "放大",
                "PtzCmd__ZoomOut": "缩小"
            },
            "x-enum-descriptions": [
                "向上移动",
                "向下移动",
                "向左移动",
                "向右移动",
                "放大",
                "缩小",
                "停止"
            ],
            "x-enum-varnames": [
                "PtzCmd__Up",
                "PtzCmd__Down",
                "PtzCmd__Left",
                "PtzCmd__Right",
                "PtzCmd__ZoomIn",
                "PtzCmd__ZoomOut",
                "PtzCmd__Stop"
            ]
        },
        "models.StopRequest": {
            "type": "object",
            "required": [
                "session_id"
            ],
            "properties": {
                "session_id": {
                    "description": "会话 ID",
                    "type": "string"
                }
            }
        },
        "models.StreamRequest": {
            "type": "object",
            "required": [
                "gb_id",
                "receive_ip",
                "receive_port"
            ],
            "properties": {
                "gb_id": {
                    "description": "GB/T 28181 设备 ID",
                    "type": "string"
                },
                "receive_ip": {
                    "description": "接收视频流的 IP 地址",
                    "type": "string"
                },
                "receive_port": {
                    "description": "接收视频流的端口号",
                    "type": "integer"
                }
            }
        },
        "models.StreamResponse": {
            "type": "object",
            "properties": {
                "session_id": {
                    "description": "会话 ID",
                    "type": "string"
                },
                "ssrc": {
                    "description": "同步源 (SSRC)",
                    "type": "string"
                }
            }
        },
        "models.UpdateSessionRequest": {
            "type": "object",
            "required": [
                "session_id"
            ],
            "properties": {
                "session_id": {
                    "description": "会话 ID",
                    "type": "string"
                }
            }
        }
    },
    "tags": [
        {
            "description": "设备管理相关接口",
            "name": "devices"
        },
        {
            "description": "视频流控制相关接口",
            "name": "stream"
        },
        {
            "description": "设备控制相关接口",
            "name": "control"
        },
        {
            "description": "健康检查相关接口",
            "name": "health"
        }
    ]
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "v0.1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{"http", "https"},
	Title:            "GB28181 Gateway API",
	Description:      "GB/T 28181 协议网关服务，提供HTTP API到SIP协议的转换",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
