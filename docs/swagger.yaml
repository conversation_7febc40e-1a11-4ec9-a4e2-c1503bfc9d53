basePath: /api/v1
definitions:
  models.APIResponse:
    properties:
      code:
        description: 错误代码
        type: integer
      data:
        description: 响应数据
      message:
        description: 错误消息
        type: string
    type: object
  models.Device:
    properties:
      address:
        description: 设备安装地址
        type: string
      block:
        description: 组织机构代码
        type: string
      business_group_id:
        description: 业务分组ID
        type: string
      civil_code:
        description: 行政区划代码
        type: string
      device_type:
        allOf:
        - $ref: '#/definitions/models.DeviceType'
        description: 新增字段用于支持递归目录查询
      gb_id:
        type: string
      ip:
        type: string
      is_real_device:
        description: 是否为真实设备（摄像头）
        type: boolean
      last_updated:
        description: 设备最后更新时间
        type: string
      manufacturer:
        description: 设备厂商
        type: string
      model:
        description: 设备型号
        type: string
      name:
        type: string
      owner:
        description: 设备归属
        type: string
      parent_id:
        description: 父节点ID
        type: string
      parental:
        description: 是否有子设备
        type: integer
      platform_id:
        type: string
      register_way:
        description: 注册方式
        type: string
      secrecy:
        description: 保密属性
        type: integer
      status:
        type: string
    type: object
  models.DeviceListResponse:
    properties:
      devices:
        description: 设备列表
        items:
          $ref: '#/definitions/models.Device'
        type: array
      pagination:
        allOf:
        - $ref: '#/definitions/models.PaginationResponse'
        description: 分页信息
    type: object
  models.DeviceType:
    enum:
    - administrative_region
    - camera
    - business_group
    - virtual_organization
    - system_directory
    - unknown
    type: string
    x-enum-comments:
      DeviceTypeAdministrativeRegion: 行政区划 (2-8位)
      DeviceTypeBusinessGroup: 业务分组 (20位,类型215)
      DeviceTypeCamera: 摄像头 (20位,类型131/132)
      DeviceTypeSystemDirectory: 系统目录 (20位,类型200)
      DeviceTypeUnknown: 未知类型
      DeviceTypeVirtualOrganization: 虚拟组织 (20位,类型216)
    x-enum-descriptions:
    - 行政区划 (2-8位)
    - 摄像头 (20位,类型131/132)
    - 业务分组 (20位,类型215)
    - 虚拟组织 (20位,类型216)
    - 系统目录 (20位,类型200)
    - 未知类型
    x-enum-varnames:
    - DeviceTypeAdministrativeRegion
    - DeviceTypeCamera
    - DeviceTypeBusinessGroup
    - DeviceTypeVirtualOrganization
    - DeviceTypeSystemDirectory
    - DeviceTypeUnknown
  models.HealthResponse:
    properties:
      status:
        description: 健康状态
        type: string
      timestamp:
        description: 时间戳
        type: integer
    type: object
  models.PTZRequest:
    properties:
      command:
        allOf:
        - $ref: '#/definitions/models.PtzCmd'
        description: 云台控制命令
      gb_id:
        description: GB/T 28181 设备 ID
        type: string
      speed:
        description: 云台控制速度
        maximum: 255
        minimum: 0
        type: integer
    required:
    - command
    - gb_id
    type: object
  models.PaginationResponse:
    properties:
      page:
        description: 当前页码
        type: integer
      page_size:
        description: 每页大小
        type: integer
      total:
        description: 总记录数
        type: integer
      total_pages:
        description: 总页数
        type: integer
    type: object
  models.PtzCmd:
    enum:
    - up
    - down
    - left
    - right
    - zoom_in
    - zoom_out
    - stop
    type: string
    x-enum-comments:
      PtzCmd__Down: 向下移动
      PtzCmd__Left: 向左移动
      PtzCmd__Right: 向右移动
      PtzCmd__Stop: 停止
      PtzCmd__Up: 向上移动
      PtzCmd__ZoomIn: 放大
      PtzCmd__ZoomOut: 缩小
    x-enum-descriptions:
    - 向上移动
    - 向下移动
    - 向左移动
    - 向右移动
    - 放大
    - 缩小
    - 停止
    x-enum-varnames:
    - PtzCmd__Up
    - PtzCmd__Down
    - PtzCmd__Left
    - PtzCmd__Right
    - PtzCmd__ZoomIn
    - PtzCmd__ZoomOut
    - PtzCmd__Stop
  models.StopRequest:
    properties:
      session_id:
        description: 会话 ID
        type: string
    required:
    - session_id
    type: object
  models.StreamRequest:
    properties:
      gb_id:
        description: GB/T 28181 设备 ID
        type: string
      receive_ip:
        description: 接收视频流的 IP 地址
        type: string
      receive_port:
        description: 接收视频流的端口号
        type: integer
    required:
    - gb_id
    - receive_ip
    - receive_port
    type: object
  models.StreamResponse:
    properties:
      session_id:
        description: 会话 ID
        type: string
      ssrc:
        description: 同步源 (SSRC)
        type: string
    type: object
  models.UpdateSessionRequest:
    properties:
      session_id:
        description: 会话 ID
        type: string
    required:
    - session_id
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: GB/T 28181 协议网关服务，提供HTTP API到SIP协议的转换
  termsOfService: http://swagger.io/terms/
  title: GB28181 Gateway API
  version: v0.1.0
paths:
  /control/ptz:
    post:
      consumes:
      - application/json
      description: 控制指定设备的云台运动，支持上下左右移动、缩放和停止
      parameters:
      - description: 云台控制参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.PTZRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功发送云台控制命令
          schema:
            $ref: '#/definitions/models.APIResponse'
        "400":
          description: 请求参数错误或无效的云台命令
          schema:
            $ref: '#/definitions/models.APIResponse'
        "500":
          description: 服务器内部错误或设备未找到
          schema:
            $ref: '#/definitions/models.APIResponse'
      summary: 云台控制
      tags:
      - control
  /devices:
    get:
      description: 根据平台ID获取该平台下的所有真实摄像头设备信息（通过递归目录查询，过滤掉行政区划、业务分组等虚拟节点），支持分页查询
      parameters:
      - description: 平台ID，不传则获取所有平台的摄像头设备
        in: query
        name: platform_id
        type: string
      - description: 页码，从1开始，默认为1
        in: query
        name: page
        type: integer
      - description: 每页大小，默认为20，最大为100
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回摄像头设备列表
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.DeviceListResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.APIResponse'
      summary: 获取摄像头设备列表
      tags:
      - devices
  /health:
    get:
      description: 检查服务器运行状态
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.HealthResponse'
      summary: 健康检查
      tags:
      - health
  /stream/request:
    post:
      consumes:
      - application/json
      description: 向指定设备请求视频流，返回SSRC和会话ID
      parameters:
      - description: 视频流请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.StreamRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功发起视频流请求
          schema:
            allOf:
            - $ref: '#/definitions/models.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.StreamResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.APIResponse'
        "500":
          description: 服务器内部错误或设备未找到
          schema:
            $ref: '#/definitions/models.APIResponse'
      summary: 请求视频流
      tags:
      - stream
  /stream/stop:
    post:
      consumes:
      - application/json
      description: 停止指定会话的视频流
      parameters:
      - description: 停止视频流请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.StopRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功停止视频流
          schema:
            $ref: '#/definitions/models.APIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.APIResponse'
        "500":
          description: 服务器内部错误或会话未找到
          schema:
            $ref: '#/definitions/models.APIResponse'
      summary: 停止视频流
      tags:
      - stream
  /stream/update:
    post:
      consumes:
      - application/json
      description: 由解码终端调用，更新指定会话的最后使用时间，用于跟踪流的活跃状态
      parameters:
      - description: 更新会话使用时间请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateSessionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功更新会话使用时间
          schema:
            $ref: '#/definitions/models.APIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.APIResponse'
        "404":
          description: 会话未找到
          schema:
            $ref: '#/definitions/models.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/models.APIResponse'
      summary: 更新会话使用时间
      tags:
      - stream
schemes:
- http
- https
swagger: "2.0"
tags:
- description: 设备管理相关接口
  name: devices
- description: 视频流控制相关接口
  name: stream
- description: 设备控制相关接口
  name: control
- description: 健康检查相关接口
  name: health
