package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"media-nexus/internal/config"
	"media-nexus/internal/core"
	"media-nexus/internal/http"
	"media-nexus/internal/sip"
	"media-nexus/internal/state"
)

func main() {
	var configPath string
	flag.StringVar(&configPath, "config", "config.yaml", "Path to configuration file")
	flag.Parse()

	// Create configuration manager
	configManager, err := config.NewManager(configPath)
	if err != nil {
		log.Fatalf("Failed to create config manager: %v", err)
	}

	// Get initial configuration
	cfg := configManager.GetConfig()

	// Setup logger
	if err := cfg.Log.SetupLogger(); err != nil {
		log.Fatalf("Failed to setup logger: %v", err)
	}

	slog.Info("Starting MediaNexus", "version", "1.0.0")

	// Create state manager
	stateManager := state.NewManager()

	// Create SIP server
	sipServer := sip.NewServer(&cfg.Server, stateManager)

	// Create core logic
	coreLogic := core.NewLogic(stateManager, sipServer)

	// Create HTTP server
	httpServer := http.NewServer(cfg, configManager, coreLogic)

	// Create context for graceful shutdown
	ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer cancel()

	// Start SIP server
	if err := sipServer.Start(ctx); err != nil {
		slog.Error("Failed to start SIP server", "error", err)
		os.Exit(1)
	}

	// Start HTTP server
	if err := httpServer.Start(ctx); err != nil {
		slog.Error("Failed to start HTTP server", "error", err)
		os.Exit(1)
	}

	slog.Info("MediaNexus started successfully")
	slog.Info("HTTP API available", "url", fmt.Sprintf("http://localhost:%d", cfg.Server.HTTPPort))
	if cfg.Log.IsDebugMode() {
		slog.Info("Swagger UI available", "url", fmt.Sprintf("http://localhost:%d/swagger/index.html", cfg.Server.HTTPPort))
	}
	if cfg.WebAdmin.Enabled {
		slog.Info("Web Admin available", "url", fmt.Sprintf("http://localhost:%d%s", cfg.Server.HTTPPort, cfg.WebAdmin.Path))
	}

	// Wait for shutdown signal
	<-ctx.Done()
	slog.Info("Shutting down MediaNexus...")

	// Create shutdown context with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Stop HTTP server
	if err := httpServer.Stop(shutdownCtx); err != nil {
		slog.Error("Failed to stop HTTP server", "error", err)
	}

	// Stop SIP server
	sipServer.Stop()

	slog.Info("MediaNexus stopped")
}
