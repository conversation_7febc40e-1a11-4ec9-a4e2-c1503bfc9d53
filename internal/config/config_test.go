package config

import (
	"log/slog"
	"os"
	"path/filepath"
	"testing"
)

func TestLoad(t *testing.T) {
	// Create a temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "config.yaml")

	// Write test config
	configContent := `
server:
  http_port: 9090
  sip_ip: "127.0.0.1"
  sip_port: 5061
  sip_id: "12345678901234567890"
  sip_domain: "1234567890"
  catalog_query_interval: 120
  device_expire_timeout: 600

log:
  level: "debug"
  path: "/tmp/test.log"

webadmin:
  enabled: false
  path: "/admin"
`

	err := os.WriteFile(configFile, []byte(configContent), 0o644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Test loading config
	config, err := Load(configFile)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify server config
	if config.Server.HTTPPort != 9090 {
		t.Errorf("Expected HTTPPort 9090, got %d", config.Server.HTTPPort)
	}

	if config.Server.SIPIP != "127.0.0.1" {
		t.Errorf("Expected SIPIP '127.0.0.1', got '%s'", config.Server.SIPIP)
	}

	if config.Server.SIPPort != 5061 {
		t.Errorf("Expected SIPPort 5061, got %d", config.Server.SIPPort)
	}

	if config.Server.SIPID != "12345678901234567890" {
		t.Errorf("Expected SIPID '12345678901234567890', got '%s'", config.Server.SIPID)
	}

	if config.Server.SIPDomain != "1234567890" {
		t.Errorf("Expected SIPDomain '1234567890', got '%s'", config.Server.SIPDomain)
	}

	if config.Server.CatalogQueryInterval != 120 {
		t.Errorf("Expected CatalogQueryInterval 120, got %d", config.Server.CatalogQueryInterval)
	}

	if config.Server.DeviceExpireTimeout != 600 {
		t.Errorf("Expected DeviceExpireTimeout 600, got %d", config.Server.DeviceExpireTimeout)
	}

	// Verify log config
	if config.Log.Level != "debug" {
		t.Errorf("Expected log level 'debug', got '%s'", config.Log.Level)
	}

	if config.Log.Path != "/tmp/test.log" {
		t.Errorf("Expected log path '/tmp/test.log', got '%s'", config.Log.Path)
	}

	// Verify webadmin config
	if config.WebAdmin.Enabled {
		t.Error("Expected WebAdmin.Enabled to be false")
	}

	if config.WebAdmin.Path != "/admin" {
		t.Errorf("Expected WebAdmin.Path '/admin', got '%s'", config.WebAdmin.Path)
	}
}

func TestLoadWithDefaults(t *testing.T) {
	// Create an empty config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "empty.yaml")

	// Write minimal config
	configContent := `# empty config file`

	err := os.WriteFile(configFile, []byte(configContent), 0o644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Test loading config with defaults
	config, err := Load(configFile)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify default values
	if config.Server.HTTPPort != 8080 {
		t.Errorf("Expected default HTTPPort 8080, got %d", config.Server.HTTPPort)
	}

	if config.Server.SIPIP != "0.0.0.0" {
		t.Errorf("Expected default SIPIP '0.0.0.0', got '%s'", config.Server.SIPIP)
	}

	if config.Server.SIPPort != 5060 {
		t.Errorf("Expected default SIPPort 5060, got %d", config.Server.SIPPort)
	}

	if config.Server.SIPID != "44010000002000000099" {
		t.Errorf("Expected default SIPID '44010000002000000099', got '%s'", config.Server.SIPID)
	}

	if config.Server.SIPDomain != "4401000000" {
		t.Errorf("Expected default SIPDomain '4401000000', got '%s'", config.Server.SIPDomain)
	}

	if config.Log.Level != "info" {
		t.Errorf("Expected default log level 'info', got '%s'", config.Log.Level)
	}

	if !config.WebAdmin.Enabled {
		t.Error("Expected default WebAdmin.Enabled to be true")
	}

	if config.WebAdmin.Path != "/config" {
		t.Errorf("Expected default WebAdmin.Path '/config', got '%s'", config.WebAdmin.Path)
	}
}

func TestLoadNonExistentFile(t *testing.T) {
	_, err := Load("non-existent-file.yaml")
	if err == nil {
		t.Error("Expected error when loading non-existent file")
	}
}

func TestLogConfigGetLogLevel(t *testing.T) {
	testCases := []struct {
		level    string
		expected slog.Level
	}{
		{"debug", slog.LevelDebug},
		{"info", slog.LevelInfo},
		{"warn", slog.LevelWarn},
		{"error", slog.LevelError},
		{"unknown", slog.LevelInfo}, // default
	}

	for _, tc := range testCases {
		logConfig := &LogConfig{Level: tc.level}
		result := logConfig.GetLogLevel()
		if result != tc.expected {
			t.Errorf("Level '%s': expected %v, got %v", tc.level, tc.expected, result)
		}
	}
}

func TestLogConfigIsDebugMode(t *testing.T) {
	testCases := []struct {
		level    string
		expected bool
	}{
		{"debug", true},
		{"info", false},
		{"warn", false},
		{"error", false},
		{"unknown", false},
	}

	for _, tc := range testCases {
		logConfig := &LogConfig{Level: tc.level}
		result := logConfig.IsDebugMode()
		if result != tc.expected {
			t.Errorf("Level '%s': expected %v, got %v", tc.level, tc.expected, result)
		}
	}
}

func TestLogConfigSetupLogger(t *testing.T) {
	// Test debug mode (stdout)
	t.Run("Debug mode", func(t *testing.T) {
		logConfig := &LogConfig{Level: "debug"}
		err := logConfig.SetupLogger()
		if err != nil {
			t.Errorf("SetupLogger failed: %v", err)
		}
	})

	// Test with log file
	t.Run("With log file", func(t *testing.T) {
		tempDir := t.TempDir()
		logFile := filepath.Join(tempDir, "test.log")

		logConfig := &LogConfig{
			Level: "info",
			Path:  logFile,
		}
		err := logConfig.SetupLogger()
		if err != nil {
			t.Errorf("SetupLogger failed: %v", err)
		}

		// Verify log file was created
		if _, err := os.Stat(logFile); os.IsNotExist(err) {
			t.Error("Log file was not created")
		}
	})

	// Test without log file (stdout)
	t.Run("Without log file", func(t *testing.T) {
		logConfig := &LogConfig{
			Level: "info",
			Path:  "",
		}
		err := logConfig.SetupLogger()
		if err != nil {
			t.Errorf("SetupLogger failed: %v", err)
		}
	})

	// Test invalid log path
	t.Run("Invalid log path", func(t *testing.T) {
		logConfig := &LogConfig{
			Level: "info",
			Path:  "/invalid/path/test.log",
		}
		err := logConfig.SetupLogger()
		if err == nil {
			t.Error("Expected error for invalid log path")
		}
	})
}
