package config

import (
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"
)

func TestNewManager(t *testing.T) {
	// Create a temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "config.yaml")

	// Write test config
	configContent := `
server:
  http_port: 8081
  sip_ip: "127.0.0.1"
  sip_port: 5061

log:
  level: "debug"

webadmin:
  enabled: true
`

	err := os.WriteFile(configFile, []byte(configContent), 0o644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Test creating manager
	manager, err := NewManager(configFile)
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	// Verify config was loaded
	config := manager.GetConfig()
	if config.Server.HTTPPort != 8081 {
		t.<PERSON><PERSON><PERSON>("Expected HTTPPort 8081, got %d", config.Server.HTTPPort)
	}

	if config.Log.Level != "debug" {
		t.<PERSON>("Expected log level 'debug', got '%s'", config.Log.Level)
	}

	if !config.WebAdmin.Enabled {
		t.Error("Expected WebAdmin.Enabled to be true")
	}
}

func TestNewManagerWithNonExistentFile(t *testing.T) {
	_, err := NewManager("non-existent-file.yaml")
	if err == nil {
		t.Error("Expected error when creating manager with non-existent file")
	}
}

func TestManagerGetConfig(t *testing.T) {
	// Create a temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "config.yaml")

	configContent := `
server:
  http_port: 9090
  sip_id: "test123"

log:
  level: "warn"
`

	err := os.WriteFile(configFile, []byte(configContent), 0o644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	manager, err := NewManager(configFile)
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	// Test getting config
	config := manager.GetConfig()
	if config == nil {
		t.Fatal("GetConfig returned nil")
	}

	if config.Server.HTTPPort != 9090 {
		t.Errorf("Expected HTTPPort 9090, got %d", config.Server.HTTPPort)
	}

	if config.Server.SIPID != "test123" {
		t.Errorf("Expected SIPID 'test123', got '%s'", config.Server.SIPID)
	}

	if config.Log.Level != "warn" {
		t.Errorf("Expected log level 'warn', got '%s'", config.Log.Level)
	}

	// Test that returned config is a copy (modifying it shouldn't affect manager)
	config.Server.HTTPPort = 9999
	config2 := manager.GetConfig()
	if config2.Server.HTTPPort == 9999 {
		t.Error("GetConfig should return a copy, not the original")
	}
}

func TestManagerUpdateConfig(t *testing.T) {
	// Create a temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "config.yaml")

	configContent := `
server:
  http_port: 8080
  sip_ip: "127.0.0.1"
  sip_port: 5060
  sip_id: "original"
  sip_domain: "original"
  catalog_query_interval: 60
  device_expire_timeout: 300

log:
  level: "info"
  path: "/tmp/original.log"

webadmin:
  enabled: true
  path: "/config"
`

	err := os.WriteFile(configFile, []byte(configContent), 0o644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	manager, err := NewManager(configFile)
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	// Create new config for update
	newConfig := &Config{
		Server: ServerConfig{
			HTTPPort:             9090,
			SIPIP:                "*************",
			SIPPort:              5061,
			SIPID:                "updated123",
			SIPDomain:            "updated456",
			CatalogQueryInterval: 120,
			DeviceExpireTimeout:  600,
		},
		Log: LogConfig{
			Level: "debug",
			Path:  "/tmp/updated.log",
		},
		WebAdmin: WebAdminConfig{
			Enabled: false,
			Path:    "/admin",
		},
	}

	// Test updating config
	err = manager.UpdateConfig(newConfig)
	if err != nil {
		t.Fatalf("Failed to update config: %v", err)
	}

	// Verify config was updated in memory
	config := manager.GetConfig()
	if config.Server.HTTPPort != 9090 {
		t.Errorf("Expected HTTPPort 9090, got %d", config.Server.HTTPPort)
	}

	if config.Server.SIPIP != "*************" {
		t.Errorf("Expected SIPIP '*************', got '%s'", config.Server.SIPIP)
	}

	if config.Server.SIPID != "updated123" {
		t.Errorf("Expected SIPID 'updated123', got '%s'", config.Server.SIPID)
	}

	if config.Log.Level != "debug" {
		t.Errorf("Expected log level 'debug', got '%s'", config.Log.Level)
	}

	if config.WebAdmin.Enabled {
		t.Error("Expected WebAdmin.Enabled to be false")
	}

	// Verify config was saved to file
	// Create a new manager to test file persistence
	manager2, err := NewManager(configFile)
	if err != nil {
		t.Fatalf("Failed to create second manager: %v", err)
	}

	config2 := manager2.GetConfig()
	if config2.Server.HTTPPort != 9090 {
		t.Errorf("Config not persisted: expected HTTPPort 9090, got %d", config2.Server.HTTPPort)
	}

	if config2.Server.SIPID != "updated123" {
		t.Errorf("Config not persisted: expected SIPID 'updated123', got '%s'", config2.Server.SIPID)
	}
}

func TestManagerConcurrentAccess(t *testing.T) {
	// Create a temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "config.yaml")

	configContent := `
server:
  http_port: 8080
  sip_id: "concurrent"

log:
  level: "info"
`

	err := os.WriteFile(configFile, []byte(configContent), 0o644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	manager, err := NewManager(configFile)
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	// Test concurrent access
	var wg sync.WaitGroup
	numGoroutines := 10

	// Start multiple goroutines reading config
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				config := manager.GetConfig()
				if config == nil {
					t.Errorf("GetConfig returned nil in goroutine %d", id)
					return
				}
				if config.Server.SIPID != "concurrent" {
					t.Errorf("Unexpected SIPID in goroutine %d: %s", id, config.Server.SIPID)
					return
				}
			}
		}(i)
	}

	// Start one goroutine updating config
	wg.Add(1)
	go func() {
		defer wg.Done()
		for i := 0; i < 10; i++ {
			newConfig := &Config{
				Server: ServerConfig{
					HTTPPort:             8080 + i,
					SIPIP:                "127.0.0.1",
					SIPPort:              5060,
					SIPID:                "concurrent",
					SIPDomain:            "4401000000",
					CatalogQueryInterval: 60,
					DeviceExpireTimeout:  300,
				},
				Log: LogConfig{
					Level: "info",
					Path:  "/var/log/media-nexus.log",
				},
				WebAdmin: WebAdminConfig{
					Enabled: true,
					Path:    "/config",
				},
			}

			err := manager.UpdateConfig(newConfig)
			if err != nil {
				t.Errorf("UpdateConfig failed: %v", err)
				return
			}
			time.Sleep(10 * time.Millisecond)
		}
	}()

	wg.Wait()
}

func TestManagerGetConfigPath(t *testing.T) {
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "config.yaml")

	configContent := `server: {}`
	err := os.WriteFile(configFile, []byte(configContent), 0o644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	manager, err := NewManager(configFile)
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	path := manager.GetConfigPath()
	if path != configFile {
		t.Errorf("Expected config path '%s', got '%s'", configFile, path)
	}
}
