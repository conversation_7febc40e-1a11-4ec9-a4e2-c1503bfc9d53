package state

import (
	"testing"
	"time"

	"media-nexus/pkg/models"
)

func TestGetRealCameraDevicesWithPagination(t *testing.T) {
	manager := NewManager()

	// 创建测试设备
	devices := []models.Device{
		{
			GBID:       "34020000001320000001",
			Name:       "Camera 1",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000002",
			Name:       "Camera 2",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000003",
			Name:       "Camera 3",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000004",
			Name:       "Camera 4",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000005",
			Name:       "Camera 5",
			Status:     "ON",
			PlatformID: "platform1",
		},
	}

	// 更新设备类型并添加到管理器
	for _, device := range devices {
		device.UpdateDeviceType()
		manager.devices.Store(device.GBID, &device)
	}

	// 测试分页查询
	t.Run("First page with 2 items", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("platform1", 1, 2)

		if total != 5 {
			t.Errorf("Expected total 5, got %d", total)
		}

		if len(cameras) != 2 {
			t.Errorf("Expected 2 cameras, got %d", len(cameras))
		}

		// 验证排序（按GBID）
		if cameras[0].GBID != "34020000001320000001" {
			t.Errorf("Expected first camera GBID to be 34020000001320000001, got %s", cameras[0].GBID)
		}

		if cameras[1].GBID != "34020000001320000002" {
			t.Errorf("Expected second camera GBID to be 34020000001320000002, got %s", cameras[1].GBID)
		}
	})

	t.Run("Second page with 2 items", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("platform1", 2, 2)

		if total != 5 {
			t.Errorf("Expected total 5, got %d", total)
		}

		if len(cameras) != 2 {
			t.Errorf("Expected 2 cameras, got %d", len(cameras))
		}

		// 验证排序（按GBID）
		if cameras[0].GBID != "34020000001320000003" {
			t.Errorf("Expected first camera GBID to be 34020000001320000003, got %s", cameras[0].GBID)
		}

		if cameras[1].GBID != "34020000001320000004" {
			t.Errorf("Expected second camera GBID to be 34020000001320000004, got %s", cameras[1].GBID)
		}
	})

	t.Run("Last page with 1 item", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("platform1", 3, 2)

		if total != 5 {
			t.Errorf("Expected total 5, got %d", total)
		}

		if len(cameras) != 1 {
			t.Errorf("Expected 1 camera, got %d", len(cameras))
		}

		// 验证排序（按GBID）
		if cameras[0].GBID != "34020000001320000005" {
			t.Errorf("Expected camera GBID to be 34020000001320000005, got %s", cameras[0].GBID)
		}
	})

	t.Run("Page beyond range", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("platform1", 10, 2)

		if total != 5 {
			t.Errorf("Expected total 5, got %d", total)
		}

		if len(cameras) != 0 {
			t.Errorf("Expected 0 cameras, got %d", len(cameras))
		}
	})

	t.Run("Empty platform", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("nonexistent", 1, 2)

		if total != 0 {
			t.Errorf("Expected total 0, got %d", total)
		}

		if len(cameras) != 0 {
			t.Errorf("Expected 0 cameras, got %d", len(cameras))
		}
	})
}

func TestGetRealCameraDevicesOrdering(t *testing.T) {
	manager := NewManager()

	// 创建测试设备（故意不按顺序添加）
	devices := []models.Device{
		{
			GBID:       "34020000001320000003",
			Name:       "Camera 3",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000001",
			Name:       "Camera 1",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000002",
			Name:       "Camera 2",
			Status:     "ON",
			PlatformID: "platform1",
		},
	}

	// 更新设备类型并添加到管理器
	for _, device := range devices {
		device.UpdateDeviceType()
		manager.devices.Store(device.GBID, &device)
	}

	// 测试排序
	cameras := manager.GetRealCameraDevices("platform1")

	if len(cameras) != 3 {
		t.Errorf("Expected 3 cameras, got %d", len(cameras))
	}

	// 验证按GBID排序
	expectedOrder := []string{
		"34020000001320000001",
		"34020000001320000002",
		"34020000001320000003",
	}

	for i, expected := range expectedOrder {
		if cameras[i].GBID != expected {
			t.Errorf("Expected camera %d GBID to be %s, got %s", i, expected, cameras[i].GBID)
		}
	}
}

// TestUpdateSessionLastUsedTime 测试更新会话最后使用时间
func TestUpdateSessionLastUsedTime(t *testing.T) {
	manager := NewManager()

	// 创建测试会话
	session := &models.StreamSession{
		SessionID:   "test-session-1",
		GBID:        "34020000001320000001",
		SSRC:        "1234567890",
		Destination: "192.168.1.100:8000",
		Status:      "active",
	}

	// 创建会话
	manager.CreateSession(session)

	// 获取初始的最后使用时间
	updatedSession, exists := manager.GetSession("test-session-1")
	if !exists {
		t.Fatal("Session should exist after creation")
	}
	initialLastUsedTime := updatedSession.LastUsedTime

	// 等待一小段时间，确保时间戳不同
	time.Sleep(10 * time.Millisecond)

	// 更新会话使用时间
	err := manager.UpdateSessionLastUsedTime("test-session-1")
	if err != nil {
		t.Errorf("UpdateSessionLastUsedTime should succeed, got error: %v", err)
	}

	// 验证时间已更新
	updatedSession, exists = manager.GetSession("test-session-1")
	if !exists {
		t.Fatal("Session should still exist after update")
	}

	if !updatedSession.LastUsedTime.After(initialLastUsedTime) {
		t.Errorf("LastUsedTime should be updated. Initial: %v, Updated: %v",
			initialLastUsedTime, updatedSession.LastUsedTime)
	}

	// 测试不存在的会话
	err = manager.UpdateSessionLastUsedTime("non-existent-session")
	if err == nil {
		t.Error("UpdateSessionLastUsedTime should return error for non-existent session")
	}

	if err.Error() != "session not found" {
		t.Errorf("Expected error 'session not found', got: %v", err)
	}

	t.Log("UpdateSessionLastUsedTime test completed successfully")
}
