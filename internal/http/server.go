package http

import (
	"context"
	"embed"
	"fmt"
	"html/template"
	"log/slog"
	"net/http"
	"time"

	_ "media-nexus/docs"
	"media-nexus/internal/config"
	"media-nexus/internal/core"
	"media-nexus/pkg/models"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

//go:embed webadmin/templates/* webadmin/static/*
var embedFS embed.FS

// @title GB28181 Gateway API
// @version v0.1.0
// @description GB/T 28181 协议网关服务，提供HTTP API到SIP协议的转换
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @host localhost:8080
// @BasePath /api/v1
// @schemes http https

// @tag.name devices
// @tag.description 设备管理相关接口

// @tag.name stream
// @tag.description 视频流控制相关接口

// @tag.name control
// @tag.description 设备控制相关接口

// @tag.name health
// @tag.description 健康检查相关接口

// Server HTTP服务器
type Server struct {
	config        *config.Config
	configManager *config.Manager
	coreLogic     *core.Logic
	engine        *gin.Engine
	httpServer    *http.Server
	logger        *slog.Logger
}

// NewServer creates a new HTTP server
func NewServer(cfg *config.Config, configManager *config.Manager, coreLogic *core.Logic) *Server {
	// Set gin mode based on log level
	if cfg.Log.IsDebugMode() {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()
	engine.Use(gin.Logger(), gin.Recovery(), corsMiddleware())

	return &Server{
		config:        cfg,
		configManager: configManager,
		coreLogic:     coreLogic,
		engine:        engine,
		logger:        slog.Default(),
	}
}

// Start starts the HTTP server
func (s *Server) Start(ctx context.Context) error {
	s.setupRoutes()

	addr := fmt.Sprintf(":%d", s.config.Server.HTTPPort)
	s.httpServer = &http.Server{
		Addr:    addr,
		Handler: s.engine,
	}

	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP server error", "error", err)
		}
	}()

	s.logger.Info("HTTP server started", "address", addr)
	return nil
}

// Stop stops the HTTP server
func (s *Server) Stop(ctx context.Context) error {
	if s.httpServer != nil {
		return s.httpServer.Shutdown(ctx)
	}
	return nil
}

// setupRoutes sets up HTTP routes
func (s *Server) setupRoutes() {
	// Health check
	s.engine.GET("/health", s.healthCheck)

	// API routes
	api := s.engine.Group("/api/v1")
	{
		api.GET("/health", s.healthCheck)
		api.GET("/devices", s.getDevices)
		api.POST("/stream/request", s.requestStream)
		api.POST("/stream/stop", s.stopStream)
		api.POST("/stream/update", s.updateSessionUsage)
		api.POST("/control/ptz", s.controlPTZ)
	}

	// Swagger 文档路由 - 仅在 debug 模式下启用
	if s.config.Log.IsDebugMode() {
		s.engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	// WebAdmin 配置管理路由
	if s.config.WebAdmin.Enabled {
		s.setupWebAdminRoutes()
	}
}

// healthCheck 健康检查
// @Summary 健康检查
// @Description 检查服务器运行状态
// @Tags health
// @Produce json
// @Success 200 {object} models.HealthResponse
// @Router /health [get]
func (s *Server) healthCheck(c *gin.Context) {
	response := models.HealthResponse{
		Status:    "ok",
		Timestamp: time.Now().Unix(),
	}
	c.JSON(http.StatusOK, response)
}

// getDevices 获取摄像头设备列表
// @Summary 获取摄像头设备列表
// @Description 根据平台ID获取该平台下的所有真实摄像头设备信息（通过递归目录查询，过滤掉行政区划、业务分组等虚拟节点），支持分页查询
// @Tags devices
// @Produce json
// @Param platform_id query string false "平台ID，不传则获取所有平台的摄像头设备"
// @Param page query int false "页码，从1开始，默认为1"
// @Param page_size query int false "每页大小，默认为20，最大为100"
// @Success 200 {object} models.APIResponse{data=models.DeviceListResponse} "成功返回摄像头设备列表"
// @Failure 400 {object} models.APIResponse "请求参数错误"
// @Failure 500 {object} models.APIResponse "服务器内部错误"
// @Router /devices [get]
func (s *Server) getDevices(c *gin.Context) {
	platformID := c.Query("platform_id")

	// 如果没有分页参数，使用原有的非分页接口
	if c.Query("page") == "" && c.Query("page_size") == "" {
		devices, err := s.coreLogic.GetDevices(platformID)
		if err != nil {
			s.logger.Error("Failed to get devices", "error", err)
			c.JSON(http.StatusInternalServerError, models.APIResponse{
				Code:    5001,
				Message: err.Error(),
			})
			return
		}
		if devices == nil {
			devices = []models.Device{}
		}
		c.JSON(http.StatusOK, models.APIResponse{
			Code:    200,
			Message: "success",
			Data:    devices,
		})
		return
	}

	// 解析分页参数
	var pagination models.PaginationRequest
	if err := c.ShouldBindQuery(&pagination); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid pagination parameters",
		})
		return
	}

	// 设置默认值
	if pagination.Page == 0 {
		pagination.Page = 1
	}
	if pagination.PageSize == 0 {
		pagination.PageSize = 20
	}

	// 限制最大页面大小
	if pagination.PageSize > 100 {
		pagination.PageSize = 100
	}

	// 使用分页接口
	devices, paginationInfo, err := s.coreLogic.GetDevicesWithPagination(platformID, pagination.Page, pagination.PageSize)
	if err != nil {
		s.logger.Error("Failed to get devices with pagination", "error", err)
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    5001,
			Message: err.Error(),
		})
		return
	}

	if devices == nil {
		devices = []models.Device{}
	}

	response := models.DeviceListResponse{
		Devices:    devices,
		Pagination: paginationInfo,
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    200,
		Message: "success",
		Data:    response,
	})
}

// requestStream 请求视频流
// @Summary 请求视频流
// @Description 向指定设备请求视频流，返回SSRC和会话ID
// @Tags stream
// @Accept json
// @Produce json
// @Param request body models.StreamRequest true "视频流请求参数"
// @Success 200 {object} models.APIResponse{data=models.StreamResponse} "成功发起视频流请求"
// @Failure 400 {object} models.APIResponse "请求参数错误"
// @Failure 500 {object} models.APIResponse "服务器内部错误或设备未找到"
// @Router /stream/request [post]
func (s *Server) requestStream(c *gin.Context) {
	var req models.StreamRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid request format",
		})
		return
	}

	response, err := s.coreLogic.RequestStream(req.GBID, req.ReceiveIP, req.ReceivePort)
	if err != nil {
		s.logger.Error("Failed to request stream", "error", err)
		statusCode := http.StatusInternalServerError
		errorCode := 5001
		if err.Error() == "device not found" {
			statusCode = http.StatusNotFound
			errorCode = 4004
		}

		c.JSON(statusCode, models.APIResponse{
			Code:    errorCode,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    200,
		Message: "stream request sent successfully",
		Data:    response,
	})
}

// stopStream 停止视频流
// @Summary 停止视频流
// @Description 停止指定会话的视频流
// @Tags stream
// @Accept json
// @Produce json
// @Param request body models.StopRequest true "停止视频流请求参数"
// @Success 200 {object} models.APIResponse "成功停止视频流"
// @Failure 400 {object} models.APIResponse "请求参数错误"
// @Failure 500 {object} models.APIResponse "服务器内部错误或会话未找到"
// @Router /stream/stop [post]
func (s *Server) stopStream(c *gin.Context) {
	var req models.StopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid request format",
		})
		return
	}

	err := s.coreLogic.StopStream(req.SessionID)
	if err != nil {
		s.logger.Error("Failed to stop stream", "error", err)
		statusCode := http.StatusInternalServerError
		errorCode := 5001
		if err.Error() == "session not found" {
			statusCode = http.StatusNotFound
			errorCode = 4005
		}

		c.JSON(statusCode, models.APIResponse{
			Code:    errorCode,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    200,
		Message: "stream stopped successfully",
	})
}

// updateSessionUsage 更新会话使用时间
// @Summary 更新会话使用时间
// @Description 由解码终端调用，更新指定会话的最后使用时间，用于跟踪流的活跃状态
// @Tags stream
// @Accept json
// @Produce json
// @Param request body models.UpdateSessionRequest true "更新会话使用时间请求参数"
// @Success 200 {object} models.APIResponse "成功更新会话使用时间"
// @Failure 400 {object} models.APIResponse "请求参数错误"
// @Failure 404 {object} models.APIResponse "会话未找到"
// @Failure 500 {object} models.APIResponse "服务器内部错误"
// @Router /stream/update [post]
func (s *Server) updateSessionUsage(c *gin.Context) {
	var req models.UpdateSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid request format",
		})
		return
	}

	err := s.coreLogic.UpdateSessionUsage(req.SessionID)
	if err != nil {
		s.logger.Error("Failed to update session usage", "error", err)
		statusCode := http.StatusInternalServerError
		errorCode := 5001
		if err.Error() == "failed to update session usage: session not found" {
			statusCode = http.StatusNotFound
			errorCode = 4005
		}

		c.JSON(statusCode, models.APIResponse{
			Code:    errorCode,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    200,
		Message: "session usage updated successfully",
	})
}

// controlPTZ 云台控制
// @Summary 云台控制
// @Description 控制指定设备的云台运动，支持上下左右移动、缩放和停止
// @Tags control
// @Accept json
// @Produce json
// @Param request body models.PTZRequest true "云台控制参数"
// @Success 200 {object} models.APIResponse "成功发送云台控制命令"
// @Failure 400 {object} models.APIResponse "请求参数错误或无效的云台命令"
// @Failure 500 {object} models.APIResponse "服务器内部错误或设备未找到"
// @Router /control/ptz [post]
func (s *Server) controlPTZ(c *gin.Context) {
	var req models.PTZRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid request format",
		})
		return
	}

	err := s.coreLogic.ControlPTZ(req.GBID, req.Command, req.Speed)
	if err != nil {
		s.logger.Error("Failed to control PTZ", "error", err)
		statusCode := http.StatusInternalServerError
		errorCode := 5001
		if err.Error() == "device not found" {
			statusCode = http.StatusNotFound
			errorCode = 4004
		}

		c.JSON(statusCode, models.APIResponse{
			Code:    errorCode,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    200,
		Message: "ptz command sent successfully",
	})
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// setupWebAdminRoutes 设置WebAdmin路由
func (s *Server) setupWebAdminRoutes() {
	// 配置管理路由组
	configGroup := s.engine.Group(s.config.WebAdmin.Path)
	{
		// 静态文件服务
		configGroup.GET("/static/*filepath", s.serveStaticFiles)

		// 配置界面
		configGroup.GET("/", s.handleConfigPage)
		configGroup.GET("/index.html", s.handleConfigPage)

		// API接口
		api := configGroup.Group("/api")
		{
			api.GET("/config", s.handleGetConfig)
			api.POST("/config", s.handleUpdateConfig)
			api.GET("/status", s.handleGetStatus)
		}
	}
}

// serveStaticFiles 服务静态文件
func (s *Server) serveStaticFiles(c *gin.Context) {
	filePath := c.Param("filepath")
	data, err := embedFS.ReadFile("webadmin/static" + filePath)
	if err != nil {
		c.String(404, "File not found")
		return
	}

	// 设置正确的Content-Type
	contentType := "text/plain"
	if len(filePath) > 3 {
		switch filePath[len(filePath)-3:] {
		case ".css":
			contentType = "text/css"
		case ".js":
			contentType = "application/javascript"
		}
	}

	c.Header("Content-Type", contentType)
	c.Data(200, contentType, data)
}

// handleConfigPage 处理配置页面请求
func (s *Server) handleConfigPage(c *gin.Context) {
	tmplData, err := embedFS.ReadFile("webadmin/templates/config.html")
	if err != nil {
		c.String(500, "Failed to load template: %v", err)
		return
	}

	tmpl, err := template.New("config").Parse(string(tmplData))
	if err != nil {
		c.String(500, "Failed to parse template: %v", err)
		return
	}

	currentConfig := s.configManager.GetConfig()

	data := struct {
		Config     *config.Config
		ConfigPath string
		BasePath   string
	}{
		Config:     currentConfig,
		ConfigPath: s.configManager.GetConfigPath(),
		BasePath:   s.config.WebAdmin.Path,
	}

	c.Header("Content-Type", "text/html; charset=utf-8")
	if err := tmpl.Execute(c.Writer, data); err != nil {
		c.String(500, "Failed to execute template: %v", err)
	}
}

// handleGetConfig 处理获取配置的API请求
func (s *Server) handleGetConfig(c *gin.Context) {
	config := s.configManager.GetConfig()
	c.JSON(200, gin.H{
		"success": true,
		"data":    config,
	})
}

// handleUpdateConfig 处理更新配置的API请求
func (s *Server) handleUpdateConfig(c *gin.Context) {
	var newConfig config.Config
	if err := c.ShouldBindJSON(&newConfig); err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"error":   "Invalid JSON format: " + err.Error(),
		})
		return
	}

	// 验证配置
	if err := s.validateConfig(&newConfig); err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"error":   "Invalid configuration: " + err.Error(),
		})
		return
	}

	// 更新配置
	if err := s.configManager.UpdateConfig(&newConfig); err != nil {
		slog.Error("Failed to update config", "error", err)
		c.JSON(500, gin.H{
			"success": false,
			"error":   "Failed to update configuration: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Configuration updated successfully. Please restart the container to apply changes.",
	})
}

// handleGetStatus 处理获取状态的API请求
func (s *Server) handleGetStatus(c *gin.Context) {
	c.JSON(200, gin.H{
		"success":   true,
		"timestamp": time.Now().Unix(),
		"server":    "MediaNexus Config Manager",
		"version":   "v0.1.0",
	})
}

// validateConfig 验证配置
func (s *Server) validateConfig(cfg *config.Config) error {
	// 验证端口范围
	if cfg.Server.HTTPPort <= 0 || cfg.Server.HTTPPort > 65535 {
		return fmt.Errorf("invalid HTTP port: %d", cfg.Server.HTTPPort)
	}
	if cfg.Server.SIPPort <= 0 || cfg.Server.SIPPort > 65535 {
		return fmt.Errorf("invalid SIP port: %d", cfg.Server.SIPPort)
	}

	// 验证SIP ID和域
	if cfg.Server.SIPID == "" {
		return fmt.Errorf("SIP ID cannot be empty")
	}
	if cfg.Server.SIPDomain == "" {
		return fmt.Errorf("SIP domain cannot be empty")
	}

	// 验证日志级别
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if !validLogLevels[cfg.Log.Level] {
		return fmt.Errorf("invalid log level: %s", cfg.Log.Level)
	}

	// 验证间隔时间
	if cfg.Server.CatalogQueryInterval <= 0 {
		return fmt.Errorf("catalog query interval must be positive")
	}
	if cfg.Server.DeviceExpireTimeout <= 0 {
		return fmt.Errorf("device expire timeout must be positive")
	}

	return nil
}
