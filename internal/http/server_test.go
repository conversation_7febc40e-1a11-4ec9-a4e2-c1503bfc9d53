package http

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"media-nexus/internal/config"
	"media-nexus/internal/core"
	"media-nexus/internal/state"
	"media-nexus/pkg/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupTestServer(t *testing.T) *Server {
	// Set gin to test mode
	gin.SetMode(gin.TestMode)

	// Create test config
	cfg := &config.Config{
		Server: config.ServerConfig{
			HTTPPort:  8080,
			SIPIP:     "127.0.0.1",
			SIPPort:   5060,
			SIPID:     "34020000002000000001",
			SIPDomain: "3402000000",
		},
		Log: config.LogConfig{
			Level: "debug",
			Path:  "",
		},
		WebAdmin: config.WebAdminConfig{
			Enabled: false,
			Path:    "/config",
		},
	}

	// Create config manager (using nil for test)
	configManager := &config.Manager{}

	// Create state manager and core logic
	stateManager := state.NewManager()
	coreLogic := core.NewLogic(stateManager, nil) // SIP server not needed for HTTP tests

	// Create server
	server := NewServer(cfg, configManager, coreLogic)
	server.setupRoutes()

	return server
}

func TestHealthCheck(t *testing.T) {
	server := setupTestServer(t)

	// Test /health endpoint
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/health", nil)
	server.engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.HealthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "ok", response.Status)
	assert.Greater(t, response.Timestamp, int64(0))
}

func TestAPIHealthCheck(t *testing.T) {
	server := setupTestServer(t)

	// Test /api/v1/health endpoint
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/health", nil)
	server.engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.HealthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "ok", response.Status)
}

func TestGetDevicesNoPlatforms(t *testing.T) {
	server := setupTestServer(t)

	// Test /api/v1/devices with no platforms registered
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/devices", nil)
	server.engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 5001, response.Code)
	assert.Contains(t, response.Message, "no platforms registered")
}

func TestGetDevicesWithPagination(t *testing.T) {
	server := setupTestServer(t)

	// Test pagination parameters
	testCases := []struct {
		name           string
		query          string
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "Valid pagination",
			query:          "?page=1&page_size=10",
			expectedStatus: http.StatusInternalServerError, // No platforms registered
			expectedCode:   5001,
		},
		{
			name:           "Invalid page",
			query:          "?page=0&page_size=10",
			expectedStatus: http.StatusBadRequest,
			expectedCode:   4000,
		},
		{
			name:           "Invalid page_size",
			query:          "?page=1&page_size=0",
			expectedStatus: http.StatusBadRequest,
			expectedCode:   4000,
		},
		{
			name:           "Large page_size gets limited",
			query:          "?page=1&page_size=200",
			expectedStatus: http.StatusInternalServerError, // No platforms, but pagination params are valid
			expectedCode:   5001,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			req, _ := http.NewRequest("GET", "/api/v1/devices"+tc.query, nil)
			server.engine.ServeHTTP(w, req)

			assert.Equal(t, tc.expectedStatus, w.Code)

			var response models.APIResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedCode, response.Code)
		})
	}
}

func TestRequestStreamInvalidJSON(t *testing.T) {
	server := setupTestServer(t)

	// Test with invalid JSON
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/stream/request", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	server.engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 4000, response.Code)
}

func TestRequestStreamMissingFields(t *testing.T) {
	server := setupTestServer(t)

	// Test with missing required fields
	invalidRequests := []models.StreamRequest{
		{},             // All fields missing
		{GBID: "test"}, // Missing ReceiveIP and ReceivePort
		{GBID: "test", ReceiveIP: "*************"},      // Missing ReceivePort
		{ReceiveIP: "*************", ReceivePort: 8000}, // Missing GBID
	}

	for i, req := range invalidRequests {
		t.Run(fmt.Sprintf("Invalid request %d", i+1), func(t *testing.T) {
			reqBody, _ := json.Marshal(req)
			w := httptest.NewRecorder()
			httpReq, _ := http.NewRequest("POST", "/api/v1/stream/request", bytes.NewBuffer(reqBody))
			httpReq.Header.Set("Content-Type", "application/json")
			server.engine.ServeHTTP(w, httpReq)

			assert.Equal(t, http.StatusBadRequest, w.Code)

			var response models.APIResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Equal(t, 4000, response.Code)
		})
	}
}

func TestStopStreamInvalidJSON(t *testing.T) {
	server := setupTestServer(t)

	// Test with invalid JSON
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/stream/stop", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	server.engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 4000, response.Code)
}

func TestControlPTZInvalidJSON(t *testing.T) {
	server := setupTestServer(t)

	// Test with invalid JSON
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/control/ptz", bytes.NewBufferString("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	server.engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 4000, response.Code)
}

func TestControlPTZInvalidCommand(t *testing.T) {
	server := setupTestServer(t)

	// Test with invalid PTZ command
	ptzReq := models.PTZRequest{
		GBID:    "34020000001310000001",
		Command: "invalid_command", // Invalid command
		Speed:   100,
	}

	reqBody, _ := json.Marshal(ptzReq)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/control/ptz", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	server.engine.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code) // Device not found

	var response models.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 4004, response.Code) // Device not found error
}

func TestServerStartStop(t *testing.T) {
	// Create a separate server for start/stop test to avoid route conflicts
	cfg := &config.Config{
		Server: config.ServerConfig{
			HTTPPort:  8081, // Use different port
			SIPIP:     "127.0.0.1",
			SIPPort:   5060,
			SIPID:     "34020000002000000001",
			SIPDomain: "3402000000",
		},
		Log: config.LogConfig{
			Level: "debug",
			Path:  "",
		},
		WebAdmin: config.WebAdminConfig{
			Enabled: false,
			Path:    "/config",
		},
	}

	configManager := &config.Manager{}
	stateManager := state.NewManager()
	coreLogic := core.NewLogic(stateManager, nil)
	server := NewServer(cfg, configManager, coreLogic)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Start server in background
	go func() {
		err := server.Start(ctx)
		assert.NoError(t, err)
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Stop server
	err := server.Stop(ctx)
	assert.NoError(t, err)
}

func TestNewServer(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			HTTPPort: 8080,
		},
		Log: config.LogConfig{
			Level: "debug",
		},
		WebAdmin: config.WebAdminConfig{
			Enabled: true,
		},
	}

	configManager := &config.Manager{}
	stateManager := state.NewManager()
	coreLogic := core.NewLogic(stateManager, nil)

	server := NewServer(cfg, configManager, coreLogic)

	assert.NotNil(t, server)
	assert.Equal(t, cfg, server.config)
	assert.Equal(t, configManager, server.configManager)
	assert.Equal(t, coreLogic, server.coreLogic)
	assert.NotNil(t, server.engine)
	assert.NotNil(t, server.logger)
}
