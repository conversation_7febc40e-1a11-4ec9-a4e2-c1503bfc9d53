/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #e0e6ed;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    background-attachment: fixed;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Animated background particles */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

/* Floating animation for background */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

/* Tech grid overlay */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(120, 119, 198, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(120, 119, 198, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: -1;
    animation: grid-move 30s linear infinite;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Tech background elements */
.tech-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.tech-circle {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    animation: float-circle 20s ease-in-out infinite;
}

.tech-circle-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.tech-circle-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 15%;
    animation-delay: -7s;
}

.tech-circle-3 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 60%;
    animation-delay: -14s;
}

@keyframes float-circle {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.3;
    }
    33% {
        transform: translateY(-30px) translateX(20px) scale(1.1);
        opacity: 0.5;
    }
    66% {
        transform: translateY(20px) translateX(-15px) scale(0.9);
        opacity: 0.2;
    }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Header */
.header {
    background: linear-gradient(135deg, rgba(18, 18, 62, 0.95) 0%, rgba(64, 58, 151, 0.95) 50%, rgba(142, 45, 226, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(120, 119, 198, 0.2);
    color: #ffffff;
    padding: 40px;
    border-radius: 20px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.header-icon {
    font-size: 3em;
    margin-bottom: 15px;
    animation: pulse-icon 3s ease-in-out infinite;
}

@keyframes pulse-icon {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.header h1 {
    font-size: 2.8em;
    margin-bottom: 10px;
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff 0%, #a8edea 50%, #fed6e3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(168, 237, 234, 0.5);
    letter-spacing: -0.02em;
}

.subtitle {
    font-size: 1.1em;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 152, 0, 0.2) 100%);
    backdrop-filter: blur(10px);
    color: #ffd700;
    padding: 15px 20px;
    border-radius: 12px;
    margin-top: 15px;
    font-weight: 500;
    border: 1px solid rgba(255, 193, 7, 0.3);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.1);
    animation: pulse-warning 2s ease-in-out infinite;
}

@keyframes pulse-warning {
    0%, 100% { box-shadow: 0 4px 15px rgba(255, 193, 7, 0.1); }
    50% { box-shadow: 0 4px 25px rgba(255, 193, 7, 0.2); }
}

/* Notification */
.notification {
    padding: 18px 25px;
    margin-bottom: 25px;
    border-radius: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notification.success {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(139, 195, 74, 0.2) 100%);
    color: #81c784;
    border-color: rgba(76, 175, 80, 0.3);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.1);
}

.notification.error {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.2) 0%, rgba(233, 30, 99, 0.2) 100%);
    color: #e57373;
    border-color: rgba(244, 67, 54, 0.3);
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.1);
}

.notification.info {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(3, 169, 244, 0.2) 100%);
    color: #64b5f6;
    border-color: rgba(33, 150, 243, 0.3);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
}

/* Form */
.config-form {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
    position: relative;
}

.config-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.5), transparent);
}

.section {
    padding: 35px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.section:last-child {
    border-bottom: none;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 35px;
    right: 35px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.2), transparent);
}

.section:first-child::before {
    display: none;
}

.section h2 {
    font-size: 1.6em;
    margin-bottom: 30px;
    color: #ffffff;
    font-weight: 600;
    padding-bottom: 15px;
    border-bottom: 2px solid transparent;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1px;
}

/* Form groups */
.form-group {
    margin-bottom: 30px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #b8c5d1;
    font-size: 0.95em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 15px 18px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    color: #ffffff;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.form-group input::placeholder,
.form-group select::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.2),
        inset 0 1px 3px rgba(0, 0, 0, 0.2),
        0 8px 25px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
    transform: scale(1.3);
    accent-color: #667eea;
}

.form-group input[type="checkbox"]:checked {
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.help-text {
    display: block;
    margin-top: 8px;
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    line-height: 1.4;
}

/* Form actions */
.form-actions {
    padding: 30px 35px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 20px;
    justify-content: flex-end;
    position: relative;
}

.form-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.3), transparent);
}

/* Buttons */
.btn {
    padding: 15px 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8e2de2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-secondary {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.8) 0%, rgba(90, 98, 104, 0.8) 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.2);
}

.btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.9) 0%, rgba(90, 98, 104, 0.9) 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(108, 117, 125, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Loading state */
.btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 22px;
    height: 22px;
    margin: -11px 0 0 -11px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 2;
}

.btn.loading::before {
    animation: none;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    body::after {
        background-size: 30px 30px;
    }

    .container {
        padding: 15px;
    }

    .header {
        padding: 25px 20px;
    }

    .header h1 {
        font-size: 2.2em;
    }

    .section {
        padding: 25px 20px;
    }

    .section::before {
        left: 20px;
        right: 20px;
    }

    .form-actions {
        padding: 25px 20px;
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
        padding: 18px 30px;
    }

    .form-group input,
    .form-group select {
        padding: 18px;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.config-form {
    animation: fadeIn 0.8s ease-out;
}

/* Additional tech animations */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.2);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
    }
}

.form-group input:focus,
.form-group select:focus {
    animation: glow 2s ease-in-out infinite;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Selection styling */
::selection {
    background: rgba(102, 126, 234, 0.3);
    color: #ffffff;
}