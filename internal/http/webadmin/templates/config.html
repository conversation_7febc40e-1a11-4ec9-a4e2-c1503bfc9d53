<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediaNexus 配置管理</title>
    <link rel="stylesheet" href="{{.BasePath}}/static/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Tech background elements -->
    <div class="tech-bg">
        <div class="tech-circle tech-circle-1"></div>
        <div class="tech-circle tech-circle-2"></div>
        <div class="tech-circle tech-circle-3"></div>
    </div>
    <div class="container">
        <header class="header">
            <div class="header-icon">🚀</div>
            <h1>MediaNexus 配置管理</h1>
            <p class="subtitle">内网管理控制台</p>
            <p class="warning">⚠️ 注意：配置保存后需要重启容器才能生效</p>
        </header>

        <div class="notification" id="notification" style="display: none;"></div>

        <form id="configForm" class="config-form">
            <div class="section">
                <h2>服务器配置</h2>
                <div class="form-group">
                    <label for="server_http_port">HTTP 端口:</label>
                    <input type="number" id="server_http_port" name="server.http_port" 
                           value="{{.Config.Server.HTTPPort}}" min="1" max="65535" required>
                    <span class="help-text">主服务HTTP API端口</span>
                </div>

                <div class="form-group">
                    <label for="server_sip_ip">SIP IP地址:</label>
                    <input type="text" id="server_sip_ip" style="width: 110px;" name="server.sip_ip" 
                           value="{{.Config.Server.SIPIP}}" required>
                    <span class="help-text">SIP服务监听的IP地址</span>
                </div>

                <div class="form-group">
                    <label for="server_sip_port">SIP 端口:</label>
                    <input type="number" id="server_sip_port" name="server.sip_port" 
                           value="{{.Config.Server.SIPPort}}" min="1" max="65535" required>
                    <span class="help-text">SIP服务监听端口</span>
                </div>

                <div class="form-group">
                    <label for="server_sip_id">SIP ID:</label>
                    <input type="text" id="server_sip_id" style="width: 160px;" name="server.sip_id" 
                           value="{{.Config.Server.SIPID}}" required>
                    <span class="help-text">网关国标ID</span>
                </div>

                <div class="form-group">
                    <label for="server_sip_domain">SIP 域:</label>
                    <input type="text" id="server_sip_domain" style="width: 100px;" name="server.sip_domain" 
                           value="{{.Config.Server.SIPDomain}}" required>
                    <span class="help-text">网关域</span>
                </div>

                <div class="form-group">
                    <label for="server_catalog_query_interval">目录查询间隔 (秒):</label>
                    <input type="number" id="server_catalog_query_interval"
                           style="width: 50px;"
                           name="server.catalog_query_interval" 
                           value="{{.Config.Server.CatalogQueryInterval}}" min="1" required>
                    <span class="help-text">设备目录查询间隔时间</span>
                </div>

                <div class="form-group">
                    <label for="server_device_expire_timeout">设备过期超时 (秒):</label>
                    <input type="number" id="server_device_expire_timeout"
                           style="width: 50px;"
                           name="server.device_expire_timeout" 
                           value="{{.Config.Server.DeviceExpireTimeout}}" min="1" required>
                    <span class="help-text">设备离线超时时间</span>
                </div>
            </div>

            <div class="section">
                <h2>日志配置</h2>
                <div class="form-group">
                    <label for="log_level">日志级别:</label>
                    <select id="log_level" name="log.level" required>
                        <option value="debug" {{if eq .Config.Log.Level "debug"}}selected{{end}}>Debug (启用API文档)</option>
                        <option value="info" {{if eq .Config.Log.Level "info"}}selected{{end}}>Info</option>
                        <option value="warn" {{if eq .Config.Log.Level "warn"}}selected{{end}}>Warn</option>
                        <option value="error" {{if eq .Config.Log.Level "error"}}selected{{end}}>Error</option>
                    </select>
                    <span class="help-text">日志输出级别</span>
                </div>

                <div class="form-group">
                    <label for="log_path">日志文件路径:</label>
                    <input type="text" id="log_path" name="log.path" 
                           value="{{.Config.Log.Path}}">
                    <span class="help-text">日志文件保存路径，留空则输出到控制台</span>
                </div>
            </div>

            <div class="section">
                <h2>Web管理配置</h2>
                <div class="form-group">
                    <label for="webadmin_enabled">启用Web管理:</label>
                    <input type="checkbox" id="webadmin_enabled" name="webadmin.enabled" 
                           {{if .Config.WebAdmin.Enabled}}checked{{end}}>
                    <span class="help-text">是否启用Web配置管理界面</span>
                </div>

                <div class="form-group">
                    <label for="webadmin_path">Web管理路径:</label>
                    <input type="text" id="webadmin_path" style="width: 80px;" name="webadmin.path" 
                           value="{{.Config.WebAdmin.Path}}" required>
                    <span class="help-text">Web管理界面访问路径</span>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" id="resetBtn" class="btn btn-secondary">重置</button>
                <button type="submit" id="saveBtn" class="btn btn-primary">保存配置</button>
            </div>
        </form>
    </div>

    <script src="{{.BasePath}}/static/script.js"></script>
</body>
</html>