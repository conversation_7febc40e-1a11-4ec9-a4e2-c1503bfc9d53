<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediaNexus 配置管理</title>
    <style>
/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #e0e6ed;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    background-attachment: fixed;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Animated background particles */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

/* Floating animation for background */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

/* Tech grid overlay */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(120, 119, 198, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(120, 119, 198, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: -1;
    animation: grid-move 30s linear infinite;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Tech background elements */
.tech-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.tech-circle {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    animation: float-circle 20s ease-in-out infinite;
}

.tech-circle-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.tech-circle-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 15%;
    animation-delay: -7s;
}

.tech-circle-3 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 60%;
    animation-delay: -14s;
}

@keyframes float-circle {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.3;
    }
    33% {
        transform: translateY(-30px) translateX(20px) scale(1.1);
        opacity: 0.5;
    }
    66% {
        transform: translateY(20px) translateX(-15px) scale(0.9);
        opacity: 0.2;
    }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Header */
.header {
    background: linear-gradient(135deg, rgba(18, 18, 62, 0.95) 0%, rgba(64, 58, 151, 0.95) 50%, rgba(142, 45, 226, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(120, 119, 198, 0.2);
    color: #ffffff;
    padding: 40px;
    border-radius: 20px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.header-icon {
    font-size: 3em;
    margin-bottom: 15px;
    animation: pulse-icon 3s ease-in-out infinite;
}

@keyframes pulse-icon {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.header h1 {
    font-size: 2.8em;
    margin-bottom: 10px;
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff 0%, #a8edea 50%, #fed6e3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(168, 237, 234, 0.5);
    letter-spacing: -0.02em;
}

.subtitle {
    font-size: 1.1em;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 152, 0, 0.2) 100%);
    backdrop-filter: blur(10px);
    color: #ffd700;
    padding: 15px 20px;
    border-radius: 12px;
    margin-top: 15px;
    font-weight: 500;
    border: 1px solid rgba(255, 193, 7, 0.3);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.1);
    animation: pulse-warning 2s ease-in-out infinite;
}

@keyframes pulse-warning {
    0%, 100% { box-shadow: 0 4px 15px rgba(255, 193, 7, 0.1); }
    50% { box-shadow: 0 4px 25px rgba(255, 193, 7, 0.2); }
}

/* Notification */
.notification {
    padding: 18px 25px;
    margin-bottom: 25px;
    border-radius: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notification.success {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(139, 195, 74, 0.2) 100%);
    color: #81c784;
    border-color: rgba(76, 175, 80, 0.3);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.1);
}

.notification.error {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.2) 0%, rgba(233, 30, 99, 0.2) 100%);
    color: #e57373;
    border-color: rgba(244, 67, 54, 0.3);
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.1);
}

.notification.info {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(3, 169, 244, 0.2) 100%);
    color: #64b5f6;
    border-color: rgba(33, 150, 243, 0.3);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
}

/* Form */
.config-form {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
    position: relative;
}

.config-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.5), transparent);
}

.section {
    padding: 35px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.section:last-child {
    border-bottom: none;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 35px;
    right: 35px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.2), transparent);
}

.section:first-child::before {
    display: none;
}

.section h2 {
    font-size: 1.6em;
    margin-bottom: 30px;
    color: #ffffff;
    font-weight: 600;
    padding-bottom: 15px;
    border-bottom: 2px solid transparent;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1px;
}

/* Form groups */
.form-group {
    margin-bottom: 30px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #b8c5d1;
    font-size: 0.95em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 15px 18px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    color: #ffffff;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.form-group input::placeholder,
.form-group select::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.2),
        inset 0 1px 3px rgba(0, 0, 0, 0.2),
        0 8px 25px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
    transform: scale(1.3);
    accent-color: #667eea;
}

.form-group input[type="checkbox"]:checked {
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.help-text {
    display: block;
    margin-top: 8px;
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    line-height: 1.4;
}

/* Form actions */
.form-actions {
    padding: 30px 35px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 20px;
    justify-content: flex-end;
    position: relative;
}

.form-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.3), transparent);
}

/* Buttons */
.btn {
    padding: 15px 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8e2de2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-secondary {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.8) 0%, rgba(90, 98, 104, 0.8) 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.2);
}

.btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, rgba(108, 117, 125, 0.9) 0%, rgba(90, 98, 104, 0.9) 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(108, 117, 125, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Loading state */
.btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 22px;
    height: 22px;
    margin: -11px 0 0 -11px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 2;
}

.btn.loading::before {
    animation: none;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    body::after {
        background-size: 30px 30px;
    }

    .container {
        padding: 15px;
    }

    .header {
        padding: 25px 20px;
    }

    .header h1 {
        font-size: 2.2em;
    }

    .section {
        padding: 25px 20px;
    }

    .section::before {
        left: 20px;
        right: 20px;
    }

    .form-actions {
        padding: 25px 20px;
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
        padding: 18px 30px;
    }

    .form-group input,
    .form-group select {
        padding: 18px;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.config-form {
    animation: fadeIn 0.8s ease-out;
}

/* Additional tech animations */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.2);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
    }
}

.form-group input:focus,
.form-group select:focus {
    animation: glow 2s ease-in-out infinite;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Selection styling */
::selection {
    background: rgba(102, 126, 234, 0.3);
    color: #ffffff;
}
    </style>
</head>
<body>
    <!-- Tech background elements -->
    <div class="tech-bg">
        <div class="tech-circle tech-circle-1"></div>
        <div class="tech-circle tech-circle-2"></div>
        <div class="tech-circle tech-circle-3"></div>
    </div>
    <div class="container">
        <header class="header">
            <div class="header-icon">🚀</div>
            <h1>MediaNexus 配置管理</h1>
            <p class="subtitle">内网管理控制台</p>
            <p class="warning">⚠️ 注意：配置保存后需要重启容器才能生效</p>
        </header>

        <div class="notification" id="notification" style="display: none;"></div>

        <form id="configForm" class="config-form">
            <div class="section">
                <h2>服务器配置</h2>
                <div class="form-group">
                    <label for="server_http_port">HTTP 端口:</label>
                    <input type="number" id="server_http_port" name="server.http_port" 
                           value="{{.Config.Server.HTTPPort}}" min="1" max="65535" required>
                    <span class="help-text">主服务HTTP API端口</span>
                </div>

                <div class="form-group">
                    <label for="server_sip_ip">SIP IP地址:</label>
                    <input type="text" id="server_sip_ip" name="server.sip_ip" 
                           value="{{.Config.Server.SIPIP}}" required>
                    <span class="help-text">SIP服务监听的IP地址</span>
                </div>

                <div class="form-group">
                    <label for="server_sip_port">SIP 端口:</label>
                    <input type="number" id="server_sip_port" name="server.sip_port" 
                           value="{{.Config.Server.SIPPort}}" min="1" max="65535" required>
                    <span class="help-text">SIP服务监听端口</span>
                </div>

                <div class="form-group">
                    <label for="server_sip_id">SIP ID:</label>
                    <input type="text" id="server_sip_id" name="server.sip_id" 
                           value="{{.Config.Server.SIPID}}" required>
                    <span class="help-text">网关国标ID</span>
                </div>

                <div class="form-group">
                    <label for="server_sip_domain">SIP 域:</label>
                    <input type="text" id="server_sip_domain" name="server.sip_domain" 
                           value="{{.Config.Server.SIPDomain}}" required>
                    <span class="help-text">网关域</span>
                </div>

                <div class="form-group">
                    <label for="server_catalog_query_interval">目录查询间隔 (秒):</label>
                    <input type="number" id="server_catalog_query_interval"
                           name="server.catalog_query_interval" 
                           value="{{.Config.Server.CatalogQueryInterval}}" min="1" required>
                    <span class="help-text">设备目录查询间隔时间</span>
                </div>

                <div class="form-group">
                    <label for="server_device_expire_timeout">设备过期超时 (秒):</label>
                    <input type="number" id="server_device_expire_timeout"
                           name="server.device_expire_timeout" 
                           value="{{.Config.Server.DeviceExpireTimeout}}" min="1" required>
                    <span class="help-text">设备离线超时时间</span>
                </div>
            </div>

            <div class="section">
                <h2>日志配置</h2>
                <div class="form-group">
                    <label for="log_level">日志级别:</label>
                    <select id="log_level" name="log.level" required>
                        <option value="debug" {{if eq .Config.Log.Level "debug"}}selected{{end}}>Debug (启用API文档)</option>
                        <option value="info" {{if eq .Config.Log.Level "info"}}selected{{end}}>Info</option>
                        <option value="warn" {{if eq .Config.Log.Level "warn"}}selected{{end}}>Warn</option>
                        <option value="error" {{if eq .Config.Log.Level "error"}}selected{{end}}>Error</option>
                    </select>
                    <span class="help-text">日志输出级别</span>
                </div>

                <div class="form-group">
                    <label for="log_path">日志文件路径:</label>
                    <input type="text" id="log_path" name="log.path" 
                           value="{{.Config.Log.Path}}">
                    <span class="help-text">日志文件保存路径，留空则输出到控制台</span>
                </div>
            </div>

            <div class="section">
                <h2>Web管理配置</h2>
                <div class="form-group">
                    <label for="webadmin_enabled">启用Web管理:</label>
                    <input type="checkbox" id="webadmin_enabled" name="webadmin.enabled" 
                           {{if .Config.WebAdmin.Enabled}}checked{{end}}>
                    <span class="help-text">是否启用Web配置管理界面</span>
                </div>

                <div class="form-group">
                    <label for="webadmin_path">Web管理路径:</label>
                    <input type="text" id="webadmin_path" name="webadmin.path" 
                           value="{{.Config.WebAdmin.Path}}" required>
                    <span class="help-text">Web管理界面访问路径</span>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" id="resetBtn" class="btn btn-secondary">重置</button>
                <button type="submit" id="saveBtn" class="btn btn-primary">保存配置</button>
            </div>
        </form>
    </div>

    <script>
// 配置管理 JavaScript

class ConfigManager {
    constructor() {
        this.form = document.getElementById('configForm');
        this.saveBtn = document.getElementById('saveBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.notification = document.getElementById('notification');

        this.originalData = {};
        this.basePath = this.getBasePath();

        this.init();
    }

    init() {
        // 保存原始数据
        this.saveOriginalData();

        // 绑定事件
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        this.resetBtn.addEventListener('click', () => this.handleReset());

        // 定期检查配置更新
        this.startConfigPolling();
    }

    getBasePath() {
        // 从当前URL获取base path
        const path = window.location.pathname;
        const parts = path.split('/');
        parts.pop(); // 移除最后的文件名或空字符串
        return parts.join('/') || '/config';
    }

    saveOriginalData() {
        const formData = new FormData(this.form);
        this.originalData = this.formDataToObject(formData);
    }

    formDataToObject(formData) {
        const obj = {};
        for (const [key, value] of formData.entries()) {
            this.setNestedProperty(obj, key, value);
        }
        return obj;
    }

    setNestedProperty(obj, path, value) {
        const keys = path.split('.');
        let current = obj;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current)) {
                current[key] = {};
            }
            current = current[key];
        }

        const lastKey = keys[keys.length - 1];

        // 需要保持为字符串的字段
        const stringFields = ['server.sip_id', 'server.sip_domain', 'log.path', 'webadmin.path'];

        // 处理不同类型的值
        if (value === 'on') {
            // 复选框选中状态
            current[lastKey] = true;
        } else if (value === '' && this.form.querySelector(`[name="${path}"]`).type === 'checkbox') {
            // 复选框未选中状态
            current[lastKey] = false;
        } else if (stringFields.includes(path)) {
            // 强制保持为字符串的字段
            current[lastKey] = value;
        } else if (!isNaN(value) && value !== '') {
            // 数字类型
            current[lastKey] = parseInt(value, 10);
        } else {
            // 字符串类型
            current[lastKey] = value;
        }
    }

    async handleSubmit(e) {
        e.preventDefault();

        this.setButtonLoading(this.saveBtn, true);

        try {
            // 获取当前配置作为基础
            const currentConfigResponse = await fetch(`${this.basePath}/api/config`);
            const currentConfigResult = await currentConfigResponse.json();

            if (!currentConfigResult.success) {
                throw new Error('无法获取当前配置');
            }

            // 使用当前配置作为基础
            const configData = JSON.parse(JSON.stringify(currentConfigResult.data));

            // 获取表单数据并更新到配置中
            const formData = new FormData(this.form);
            for (const [key, value] of formData.entries()) {
                this.setNestedProperty(configData, key, value);
            }

            // 处理复选框（未选中的复选框不会出现在FormData中）
            const checkboxes = this.form.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (!formData.has(checkbox.name)) {
                    this.setNestedProperty(configData, checkbox.name, false);
                }
            });

            console.log('Submitting config:', configData);

            // 发送请求
            const response = await fetch(`${this.basePath}/api/config`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configData)
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('配置保存成功！请重启容器使配置生效。', 'success');
                this.saveOriginalData(); // 更新原始数据
            } else {
                throw new Error(result.error || '保存失败');
            }

        } catch (error) {
            console.error('Error saving config:', error);
            this.showNotification(`保存失败：${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.saveBtn, false);
        }
    }

    handleReset() {
        if (confirm('确定要重置所有更改吗？')) {
            this.form.reset();
            this.populateForm(this.originalData);
            this.showNotification('表单已重置到原始状态', 'info');
        }
    }

    populateForm(data) {
        // 递归填充表单
        this.populateFormRecursive(data, '');
    }

    populateFormRecursive(obj, prefix) {
        for (const [key, value] of Object.entries(obj)) {
            const fieldName = prefix ? `${prefix}.${key}` : key;
            const field = this.form.querySelector(`[name="${fieldName}"]`);

            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = Boolean(value);
                } else {
                    field.value = value;
                }
            } else if (typeof value === 'object' && value !== null) {
                this.populateFormRecursive(value, fieldName);
            }
        }
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            button.setAttribute('data-original-text', button.textContent);
            button.textContent = '保存中...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.textContent = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }

    showNotification(message, type = 'info') {
        this.notification.textContent = message;
        this.notification.className = `notification ${type}`;
        this.notification.style.display = 'block';

        // 自动隐藏
        setTimeout(() => {
            this.notification.style.display = 'none';
        }, 5000);
    }

    async startConfigPolling() {
        // 每30秒检查一次配置是否有外部更新
        setInterval(async () => {
            try {
                const response = await fetch(`${this.basePath}/api/config`);
                const result = await response.json();

                if (result.success) {
                    // 这里可以添加逻辑来检测配置是否被外部修改
                    // 如果需要，可以提示用户重新加载页面
                }
            } catch (error) {
                console.warn('Failed to check config updates:', error);
            }
        }, 30000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ConfigManager();
});
    </script>
</body>
</html>