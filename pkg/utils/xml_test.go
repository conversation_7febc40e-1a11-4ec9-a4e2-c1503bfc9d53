package utils

import (
	"testing"

	"media-nexus/pkg/models"
)

func TestXMLDecode(t *testing.T) {
	// Test Catalog response
	catalogXML := `<?xml version="1.0" encoding="UTF-8"?>
<Response>
	<CmdType>Catalog</CmdType>
	<SN>123</SN>
	<DeviceID>34020000002000000001</DeviceID>
	<SumNum>2</SumNum>
	<DeviceList Num="2">
		<Item>
			<DeviceID>34020000001320000001</DeviceID>
			<Name>Camera 1</Name>
			<Status>ON</Status>
			<IPAddress>*************</IPAddress>
		</Item>
		<Item>
			<DeviceID>34020000001320000002</DeviceID>
			<Name>Camera 2</Name>
			<Status>OFF</Status>
			<IPAddress>*************</IPAddress>
		</Item>
	</DeviceList>
</Response>`

	var catalogResp models.CatalogResponse
	err := XMLDecode([]byte(catalogXML), &catalogResp)
	if err != nil {
		t.Fatalf("Failed to decode catalog XML: %v", err)
	}

	if catalogResp.CmdType != "Catalog" {
		t.Errorf("Expected CmdType 'Catalog', got '%s'", catalogResp.CmdType)
	}

	if catalogResp.SN != 123 {
		t.Errorf("Expected SN 123, got %d", catalogResp.SN)
	}

	if catalogResp.SumNum != 2 {
		t.Errorf("Expected SumNum 2, got %d", catalogResp.SumNum)
	}

	if len(catalogResp.DeviceList.Devices) != 2 {
		t.Errorf("Expected 2 devices, got %d", len(catalogResp.DeviceList.Devices))
	}

	// Check first device
	device1 := catalogResp.DeviceList.Devices[0]
	if device1.GBID != "34020000001320000001" {
		t.Errorf("Expected device1 GBID '34020000001320000001', got '%s'", device1.GBID)
	}
	if device1.Name != "Camera 1" {
		t.Errorf("Expected device1 Name 'Camera 1', got '%s'", device1.Name)
	}
	if device1.Status != "ON" {
		t.Errorf("Expected device1 Status 'ON', got '%s'", device1.Status)
	}
}

func TestKeepaliveXMLDecode(t *testing.T) {
	keepaliveXML := `<?xml version="1.0" encoding="UTF-8"?>
<Notify>
	<CmdType>Keepalive</CmdType>
	<SN>456</SN>
	<DeviceID>34020000002000000001</DeviceID>
	<Status>OK</Status>
</Notify>`

	var keepalive models.KeepaliveNotify
	err := XMLDecode([]byte(keepaliveXML), &keepalive)
	if err != nil {
		t.Fatalf("Failed to decode keepalive XML: %v", err)
	}

	if keepalive.CmdType != "Keepalive" {
		t.Errorf("Expected CmdType 'Keepalive', got '%s'", keepalive.CmdType)
	}

	if keepalive.SN != 456 {
		t.Errorf("Expected SN 456, got %d", keepalive.SN)
	}

	if keepalive.DeviceID != "34020000002000000001" {
		t.Errorf("Expected DeviceID '34020000002000000001', got '%s'", keepalive.DeviceID)
	}

	if keepalive.Status != "OK" {
		t.Errorf("Expected Status 'OK', got '%s'", keepalive.Status)
	}
}

func TestDeviceInfoXMLDecode(t *testing.T) {
	deviceInfoXML := `<?xml version="1.0" encoding="UTF-8"?>
<Response>
	<CmdType>DeviceInfo</CmdType>
	<SN>789</SN>
	<DeviceID>34020000002000000001</DeviceID>
	<DeviceName>Hikvision Platform</DeviceName>
	<Manufacturer>Hikvision</Manufacturer>
	<Model>DS-7816N-K2</Model>
	<Firmware>V4.30.000</Firmware>
	<Channel>16</Channel>
</Response>`

	var deviceInfo models.DeviceInfoResponse
	err := XMLDecode([]byte(deviceInfoXML), &deviceInfo)
	if err != nil {
		t.Fatalf("Failed to decode device info XML: %v", err)
	}

	if deviceInfo.CmdType != "DeviceInfo" {
		t.Errorf("Expected CmdType 'DeviceInfo', got '%s'", deviceInfo.CmdType)
	}

	if deviceInfo.SN != 789 {
		t.Errorf("Expected SN 789, got %d", deviceInfo.SN)
	}

	if deviceInfo.DeviceName != "Hikvision Platform" {
		t.Errorf("Expected DeviceName 'Hikvision Platform', got '%s'", deviceInfo.DeviceName)
	}

	if deviceInfo.Manufacturer != "Hikvision" {
		t.Errorf("Expected Manufacturer 'Hikvision', got '%s'", deviceInfo.Manufacturer)
	}

	if deviceInfo.Model != "DS-7816N-K2" {
		t.Errorf("Expected Model 'DS-7816N-K2', got '%s'", deviceInfo.Model)
	}
}
