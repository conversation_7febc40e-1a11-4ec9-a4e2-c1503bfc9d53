package models

import (
	"testing"
	"time"
)

func TestGetDeviceType(t *testing.T) {
	testCases := []struct {
		deviceID     string
		expectedType DeviceType
	}{
		// 行政区划测试
		{"34", DeviceTypeAdministrativeRegion},       // 2位
		{"3402", DeviceTypeAdministrativeRegion},     // 4位
		{"340200", DeviceTypeAdministrativeRegion},   // 6位
		{"34020000", DeviceTypeAdministrativeRegion}, // 8位

		// 摄像头测试
		{"34020000001310000001", DeviceTypeCamera}, // 131类型摄像机
		{"34020000001320000001", DeviceTypeCamera}, // 132类型网络摄像机

		// 业务分组测试
		{"34020000002150000001", DeviceTypeBusinessGroup}, // 215类型

		// 虚拟组织测试
		{"34020000002160000001", DeviceTypeVirtualOrganization}, // 216类型

		// 系统目录测试
		{"34020000002000000001", DeviceTypeSystemDirectory}, // 200类型

		// 未知类型测试
		{"34020000009990000001", DeviceTypeUnknown}, // 999未知类型
		{"340200000013", DeviceTypeUnknown},         // 不足20位
		{"invalid", DeviceTypeUnknown},              // 无效格式
		{"", DeviceTypeUnknown},                     // 空字符串
	}

	for _, tc := range testCases {
		result := GetDeviceType(tc.deviceID)
		if result != tc.expectedType {
			t.Errorf("GetDeviceType(%s): expected %s, got %s",
				tc.deviceID, tc.expectedType, result)
		}
	}
}

func TestDeviceIsRealCamera(t *testing.T) {
	testCases := []struct {
		device   Device
		expected bool
	}{
		{
			device: Device{
				GBID:       "34020000001310000001",
				DeviceType: DeviceTypeCamera,
			},
			expected: true,
		},
		{
			device: Device{
				GBID:       "34020000002150000001",
				DeviceType: DeviceTypeBusinessGroup,
			},
			expected: false,
		},
		{
			device: Device{
				GBID:       "34020000002000000001",
				DeviceType: DeviceTypeSystemDirectory,
			},
			expected: false,
		},
		{
			device: Device{
				GBID:       "invalid",
				DeviceType: DeviceTypeUnknown,
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		result := tc.device.IsRealCamera()
		if result != tc.expected {
			t.Errorf("Device %s IsRealCamera(): expected %v, got %v",
				tc.device.GBID, tc.expected, result)
		}
	}
}

func TestDeviceUpdateDeviceType(t *testing.T) {
	testCases := []struct {
		gbid               string
		expectedType       DeviceType
		expectedRealDevice bool
	}{
		{"34020000001310000001", DeviceTypeCamera, true},
		{"34020000001320000001", DeviceTypeCamera, true},
		{"34020000002150000001", DeviceTypeBusinessGroup, false},
		{"34020000002160000001", DeviceTypeVirtualOrganization, false},
		{"34020000002000000001", DeviceTypeSystemDirectory, false},
		{"34", DeviceTypeAdministrativeRegion, false},
		{"invalid", DeviceTypeUnknown, false},
	}

	for _, tc := range testCases {
		device := Device{
			GBID: tc.gbid,
			Name: "Test Device",
		}

		// 调用UpdateDeviceType
		device.UpdateDeviceType()

		// 验证DeviceType
		if device.DeviceType != tc.expectedType {
			t.Errorf("Device %s UpdateDeviceType(): expected type %s, got %s",
				tc.gbid, tc.expectedType, device.DeviceType)
		}

		// 验证IsRealDevice
		if device.IsRealDevice != tc.expectedRealDevice {
			t.Errorf("Device %s UpdateDeviceType(): expected IsRealDevice %v, got %v",
				tc.gbid, tc.expectedRealDevice, device.IsRealDevice)
		}
	}
}

func TestDeviceJSONSerialization(t *testing.T) {
	device := Device{
		GBID:         "34020000001310000001",
		Name:         "Test Camera",
		Status:       "1",
		IP:           "*************",
		PlatformID:   "platform1",
		DeviceType:   DeviceTypeCamera,
		Manufacturer: "Hikvision",
		Model:        "DS-2CD2T47G1-L",
		LastUpdated:  time.Now(),
		IsRealDevice: true,
	}

	// 验证关键字段值
	if device.GBID != "34020000001310000001" {
		t.Errorf("Expected GBID '34020000001310000001', got '%s'", device.GBID)
	}

	if device.Name != "Test Camera" {
		t.Errorf("Expected Name 'Test Camera', got '%s'", device.Name)
	}

	if device.Status != "1" {
		t.Errorf("Expected Status '1', got '%s'", device.Status)
	}

	if device.DeviceType != DeviceTypeCamera {
		t.Errorf("Expected DeviceType %s, got %s", DeviceTypeCamera, device.DeviceType)
	}

	if !device.IsRealDevice {
		t.Error("Expected IsRealDevice to be true")
	}
}

func TestDeviceConstants(t *testing.T) {
	// 验证设备类型常量
	expectedTypes := map[DeviceType]string{
		DeviceTypeAdministrativeRegion: "administrative_region",
		DeviceTypeCamera:               "camera",
		DeviceTypeBusinessGroup:        "business_group",
		DeviceTypeVirtualOrganization:  "virtual_organization",
		DeviceTypeSystemDirectory:      "system_directory",
		DeviceTypeUnknown:              "unknown",
	}

	for deviceType, expectedString := range expectedTypes {
		if string(deviceType) != expectedString {
			t.Errorf("DeviceType constant mismatch: expected '%s', got '%s'",
				expectedString, string(deviceType))
		}
	}
}

func TestDeviceList(t *testing.T) {
	devices := []Device{
		{
			GBID:       "34020000001310000001",
			Name:       "Camera 1",
			DeviceType: DeviceTypeCamera,
		},
		{
			GBID:       "34020000001310000002",
			Name:       "Camera 2",
			DeviceType: DeviceTypeCamera,
		},
	}

	deviceList := DeviceList{
		Devices: devices,
		Total:   len(devices),
	}

	if len(deviceList.Devices) != 2 {
		t.Errorf("Expected 2 devices, got %d", len(deviceList.Devices))
	}

	if deviceList.Total != 2 {
		t.Errorf("Expected Total 2, got %d", deviceList.Total)
	}

	// 验证设备内容
	if deviceList.Devices[0].GBID != "34020000001310000001" {
		t.Errorf("Expected first device GBID '34020000001310000001', got '%s'",
			deviceList.Devices[0].GBID)
	}

	if deviceList.Devices[1].Name != "Camera 2" {
		t.Errorf("Expected second device Name 'Camera 2', got '%s'",
			deviceList.Devices[1].Name)
	}
}
