package models

import "encoding/xml"

type CmdType string

const (
	CmdType__Catalog        CmdType = "Catalog"
	CmdType__Keepalive      CmdType = "Keepalive"
	CmdType__RecordInfo     CmdType = "RecordInfo"
	CmdType__DeviceInfo     CmdType = "DeviceInfo"
	CmdType__ConfigDownload CmdType = "ConfigDownload"
	CmdType__Broadcast      CmdType = "Broadcast"
	CmdType__PresetQuery    CmdType = "PresetQuery"
	CmdType__DeviceControl  CmdType = "DeviceControl"
)

// MessageReceive represents the outer layer of received request data, mainly used to determine data type
type MessageReceive struct {
	CmdType CmdType `xml:"CmdType"`
	SN      int     `xml:"SN"`
}

// CatalogResponse represents catalog response from platform
type CatalogResponse struct {
	XMLName    xml.Name `xml:"Response"`
	CmdType    CmdType  `xml:"CmdType"`
	SN         int      `xml:"SN"`
	DeviceID   string   `xml:"DeviceID"`
	SumNum     int      `xml:"SumNum"`
	DeviceList struct {
		Devices []Device `xml:"Item"`
	} `xml:"DeviceList"`
}

// DeviceInfoResponse represents device info response from platform
type DeviceInfoResponse struct {
	XMLName      xml.Name `xml:"Response"`
	CmdType      CmdType  `xml:"CmdType"`
	SN           int      `xml:"SN"`
	DeviceID     string   `xml:"DeviceID"`
	DeviceName   string   `xml:"DeviceName"`
	Manufacturer string   `xml:"Manufacturer"`
	Model        string   `xml:"Model"`
	Firmware     string   `xml:"Firmware"`
	Channel      int      `xml:"Channel"`
}

// KeepaliveNotify represents keepalive notification from platform
type KeepaliveNotify struct {
	XMLName  xml.Name `xml:"Notify"`
	CmdType  CmdType  `xml:"CmdType"`
	SN       int      `xml:"SN"`
	DeviceID string   `xml:"DeviceID"`
	Status   string   `xml:"Status"`
}

type Query struct {
	XMLName  xml.Name `xml:"Query"`
	CmdType  CmdType  `xml:"CmdType"`
	SN       int      `xml:"SN"`
	DeviceID string   `xml:"DeviceID"`
}

// PTZControl represents PTZ control command
type PTZControl struct {
	XMLName  xml.Name `xml:"Control"`
	CmdType  CmdType  `xml:"CmdType"`
	SN       int      `xml:"SN"`
	DeviceID string   `xml:"DeviceID"`
	PTZCmd   string   `xml:"PTZCmd"`
	Info     PTZInfo  `xml:"Info"`
}

// PTZInfo represents PTZ control info
type PTZInfo struct {
	ControlPriority int `xml:"ControlPriority"`
}
