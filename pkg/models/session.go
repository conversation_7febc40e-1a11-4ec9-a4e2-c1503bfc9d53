package models

import (
	"time"

	sip "github.com/panjjo/gosip/sip/s"
)

// StreamSession represents a video stream session
type StreamSession struct {
	SessionID    string    `json:"session_id"`
	GBID         string    `json:"gb_id"`
	SSRC         string    `json:"ssrc"`
	DialogID     string    `json:"dialog_id"`
	Destination  string    `json:"destination"` // "ip:port"
	StartTime    time.Time `json:"start_time"`
	LastUsedTime time.Time `json:"last_used_time"` // 最后使用时间，用于解码终端更新
	Status       string    `json:"status"`         // "requesting", "active", "closing", "closed", "closing_failed", "error"
	Response     *sip.Response
}
