package models

import (
	"testing"
)

func TestAPIResponse(t *testing.T) {
	// Test successful response
	t.Run("Success response", func(t *testing.T) {
		resp := APIResponse{
			Code:    200,
			Message: "success",
			Data:    "test data",
		}

		if resp.Code != 200 {
			t.<PERSON><PERSON><PERSON>("Expected Code 200, got %d", resp.Code)
		}

		if resp.Message != "success" {
			t.<PERSON><PERSON>("Expected Message 'success', got '%s'", resp.Message)
		}

		if resp.Data != "test data" {
			t.<PERSON><PERSON>("Expected Data 'test data', got '%v'", resp.Data)
		}
	})

	// Test error response
	t.Run("Error response", func(t *testing.T) {
		resp := APIResponse{
			Code:    4000,
			Message: "Bad Request",
		}

		if resp.Code != 4000 {
			t.<PERSON>rrorf("Expected Code 4000, got %d", resp.Code)
		}

		if resp.Message != "Bad Request" {
			t.<PERSON><PERSON><PERSON>("Expected Message 'Bad Request', got '%s'", resp.Message)
		}

		if resp.Data != nil {
			t.<PERSON><PERSON><PERSON>("Expected Data to be nil, got '%v'", resp.Data)
		}
	})
}

func TestStreamRequest(t *testing.T) {
	req := StreamRequest{
		GBID:        "34020000001310000001",
		ReceiveIP:   "*************",
		ReceivePort: 8000,
	}

	if req.GBID != "34020000001310000001" {
		t.Errorf("Expected GBID '34020000001310000001', got '%s'", req.GBID)
	}

	if req.ReceiveIP != "*************" {
		t.Errorf("Expected ReceiveIP '*************', got '%s'", req.ReceiveIP)
	}

	if req.ReceivePort != 8000 {
		t.Errorf("Expected ReceivePort 8000, got %d", req.ReceivePort)
	}
}

func TestStreamResponse(t *testing.T) {
	resp := StreamResponse{
		SSRC:      "0123456789",
		SessionID: "session-123",
	}

	if resp.SSRC != "0123456789" {
		t.Errorf("Expected SSRC '0123456789', got '%s'", resp.SSRC)
	}

	if resp.SessionID != "session-123" {
		t.Errorf("Expected SessionID 'session-123', got '%s'", resp.SessionID)
	}
}

func TestStopRequest(t *testing.T) {
	req := StopRequest{
		SessionID: "session-456",
	}

	if req.SessionID != "session-456" {
		t.Errorf("Expected SessionID 'session-456', got '%s'", req.SessionID)
	}
}

func TestUpdateSessionRequest(t *testing.T) {
	req := UpdateSessionRequest{
		SessionID: "session-789",
	}

	if req.SessionID != "session-789" {
		t.Errorf("Expected SessionID 'session-789', got '%s'", req.SessionID)
	}
}

func TestPtzCmdConstants(t *testing.T) {
	expectedCommands := map[PtzCmd]string{
		PtzCmd__Up:      "up",
		PtzCmd__Down:    "down",
		PtzCmd__Left:    "left",
		PtzCmd__Right:   "right",
		PtzCmd__ZoomIn:  "zoom_in",
		PtzCmd__ZoomOut: "zoom_out",
		PtzCmd__Stop:    "stop",
	}

	for cmd, expectedString := range expectedCommands {
		if string(cmd) != expectedString {
			t.Errorf("PtzCmd constant mismatch: expected '%s', got '%s'",
				expectedString, string(cmd))
		}
	}
}

func TestPTZRequest(t *testing.T) {
	req := PTZRequest{
		GBID:    "34020000001310000001",
		Command: PtzCmd__Up,
		Speed:   100,
	}

	if req.GBID != "34020000001310000001" {
		t.Errorf("Expected GBID '34020000001310000001', got '%s'", req.GBID)
	}

	if req.Command != PtzCmd__Up {
		t.Errorf("Expected Command '%s', got '%s'", PtzCmd__Up, req.Command)
	}

	if req.Speed != 100 {
		t.Errorf("Expected Speed 100, got %d", req.Speed)
	}
}

func TestPTZRequestValidCommands(t *testing.T) {
	validCommands := []PtzCmd{
		PtzCmd__Up,
		PtzCmd__Down,
		PtzCmd__Left,
		PtzCmd__Right,
		PtzCmd__ZoomIn,
		PtzCmd__ZoomOut,
		PtzCmd__Stop,
	}

	for _, cmd := range validCommands {
		req := PTZRequest{
			GBID:    "34020000001310000001",
			Command: cmd,
			Speed:   50,
		}

		if req.Command != cmd {
			t.Errorf("PTZ command not set correctly: expected '%s', got '%s'",
				cmd, req.Command)
		}
	}
}

func TestHealthResponse(t *testing.T) {
	resp := HealthResponse{
		Status:    "ok",
		Timestamp: **********,
	}

	if resp.Status != "ok" {
		t.Errorf("Expected Status 'ok', got '%s'", resp.Status)
	}

	if resp.Timestamp != ********** {
		t.Errorf("Expected Timestamp **********, got %d", resp.Timestamp)
	}
}

func TestPaginationRequest(t *testing.T) {
	req := PaginationRequest{
		Page:     2,
		PageSize: 20,
	}

	if req.Page != 2 {
		t.Errorf("Expected Page 2, got %d", req.Page)
	}

	if req.PageSize != 20 {
		t.Errorf("Expected PageSize 20, got %d", req.PageSize)
	}
}

func TestPaginationResponse(t *testing.T) {
	resp := PaginationResponse{
		Page:       1,
		PageSize:   10,
		Total:      100,
		TotalPages: 10,
	}

	if resp.Page != 1 {
		t.Errorf("Expected Page 1, got %d", resp.Page)
	}

	if resp.PageSize != 10 {
		t.Errorf("Expected PageSize 10, got %d", resp.PageSize)
	}

	if resp.Total != 100 {
		t.Errorf("Expected Total 100, got %d", resp.Total)
	}

	if resp.TotalPages != 10 {
		t.Errorf("Expected TotalPages 10, got %d", resp.TotalPages)
	}
}

func TestDeviceListResponse(t *testing.T) {
	devices := []Device{
		{
			GBID: "34020000001310000001",
			Name: "Camera 1",
		},
		{
			GBID: "34020000001310000002",
			Name: "Camera 2",
		},
	}

	pagination := PaginationResponse{
		Page:       1,
		PageSize:   2,
		Total:      2,
		TotalPages: 1,
	}

	resp := DeviceListResponse{
		Devices:    devices,
		Pagination: pagination,
	}

	if len(resp.Devices) != 2 {
		t.Errorf("Expected 2 devices, got %d", len(resp.Devices))
	}

	if resp.Devices[0].GBID != "34020000001310000001" {
		t.Errorf("Expected first device GBID '34020000001310000001', got '%s'",
			resp.Devices[0].GBID)
	}

	if resp.Pagination.Page != 1 {
		t.Errorf("Expected pagination Page 1, got %d", resp.Pagination.Page)
	}

	if resp.Pagination.Total != 2 {
		t.Errorf("Expected pagination Total 2, got %d", resp.Pagination.Total)
	}
}
