package models

// APIResponse represents standard API response
type APIResponse struct {
	Code    int    `json:"code"`           // 错误代码
	Message string `json:"message"`        // 错误消息
	Data    any    `json:"data,omitempty"` // 响应数据
}

// StreamRequest represents stream request payload
type StreamRequest struct {
	GBID        string `json:"gb_id" binding:"required"`        // GB/T 28181 设备 ID
	ReceiveIP   string `json:"receive_ip" binding:"required"`   // 接收视频流的 IP 地址
	ReceivePort int    `json:"receive_port" binding:"required"` // 接收视频流的端口号
}

// StreamResponse represents stream request response data
type StreamResponse struct {
	SSRC      string `json:"ssrc"`       // 同步源 (SSRC)
	SessionID string `json:"session_id"` // 会话 ID
}

// StopRequest represents stop stream request payload
type StopRequest struct {
	SessionID string `json:"session_id" binding:"required"` // 会话 ID
}

// UpdateSessionRequest represents update session last used time request payload
type UpdateSessionRequest struct {
	SessionID string `json:"session_id" binding:"required"` // 会话 ID
}

type PtzCmd string // 云台控制命令

const (
	PtzCmd__Up      PtzCmd = "up"       // 向上移动
	PtzCmd__Down    PtzCmd = "down"     // 向下移动
	PtzCmd__Left    PtzCmd = "left"     // 向左移动
	PtzCmd__Right   PtzCmd = "right"    // 向右移动
	PtzCmd__ZoomIn  PtzCmd = "zoom_in"  // 放大
	PtzCmd__ZoomOut PtzCmd = "zoom_out" // 缩小
	PtzCmd__Stop    PtzCmd = "stop"     // 停止
)

// PTZRequest represents PTZ control request payload
type PTZRequest struct {
	GBID    string `json:"gb_id" binding:"required"`      // GB/T 28181 设备 ID
	Command PtzCmd `json:"command" binding:"required"`    // 云台控制命令
	Speed   int    `json:"speed" binding:"min=0,max=255"` // 云台控制速度
}

// HealthResponse represents health check response
type HealthResponse struct {
	Status    string `json:"status"`    // 健康状态
	Timestamp int64  `json:"timestamp"` // 时间戳
}

// PaginationRequest represents pagination parameters
type PaginationRequest struct {
	Page     int `form:"page" binding:"min=1"`      // 页码，从1开始
	PageSize int `form:"page_size" binding:"min=1"` // 每页大小
}

// PaginationResponse represents pagination information in response
type PaginationResponse struct {
	Page       int `json:"page"`        // 当前页码
	PageSize   int `json:"page_size"`   // 每页大小
	Total      int `json:"total"`       // 总记录数
	TotalPages int `json:"total_pages"` // 总页数
}

// DeviceListResponse represents paginated device list response
type DeviceListResponse struct {
	Devices    []Device           `json:"devices"`    // 设备列表
	Pagination PaginationResponse `json:"pagination"` // 分页信息
}
