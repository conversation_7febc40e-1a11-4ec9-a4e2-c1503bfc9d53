# GitLab CI/CD 配置文件 - 多架构Docker镜像构建
# 支持构建 amd64 和 arm64 架构的Docker镜像

stages:
  - test
  - build_docker

variables:
  PLATFORMS: "linux/amd64,linux/arm64"

# 单元测试阶段 - 仅在标签推送时运行
test:
  stage: test
  image: *************/golang:1.25.1-alpine3.22
  before_script:
    # 设置Go模块代理和私有仓库配置
    - export GOPROXY=http://*************:8081/repository/goproxy/,direct
    - export GOSUMDB=sum.golang.google.cn
    - go version
    - go env
  script:
    # 下载依赖
    - go mod download
    - go mod tidy
    # 获取所有包，并排除 cmd 和 docs 目录
    - |
      TEST_PACKAGES=$(go list ./... | grep -vE 'media-nexus/(cmd|docs)')
      echo "--- Running tests for the following packages ---"
      echo "${TEST_PACKAGES}"
      echo "------------------------------------------------"
    # 运行单元测试并生成覆盖率报告
    # 将过滤后的包列表传递给 go test
    - echo "Running unit tests and generating coverage report..."
    - go test -v -coverprofile=coverage.out -coverpkg=$(echo $TEST_PACKAGES | tr ' ' ',') -timeout 120s $TEST_PACKAGES
    # 显式输出覆盖率摘要
    - "go tool cover -func=coverage.out | grep total:"
    # 生成Cobertura报告
    - go install github.com/t-yuki/gocover-cobertura@latest
    - $(go env GOPATH)/bin/gocover-cobertura < coverage.out > coverage.xml
    - go tool cover -html=coverage.out -o coverage.html
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - coverage.html
      - coverage.out
      - coverage.xml
    expire_in: 1 week
  coverage: '/^total:\s+\(statements\)\s+(\d+\.\d+\%)/' #'/^total:.*?(\d+\.\d+)%/'
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"
  tags:
    - shared

build_docker:
  stage: build_docker
  image: *************/docker:24-dind
  services:
    - *************/docker:24-dind
  dependencies:
    - test  # 依赖测试阶段的成功
  needs:
    - job: test
      artifacts: false  # 不需要传递artifacts，只需要测试成功
  before_script:
    # 检查Docker buildx是否支持多架构
    - |
      if ! docker buildx inspect | grep -q "linux/amd64"; then
        echo "ERROR: amd64 platform not supported"
        exit 1
      fi
      if ! docker buildx inspect | grep -q "linux/arm64"; then
        echo "ERROR: arm64 platform not supported"
        exit 1
      fi
    # 登录到Docker仓库
    - docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD $DOCKER_IMAGE_SERVER
    - docker buildx version
    - docker context create builder-context || true
    # docs: https://docs.docker.com/build/buildkit/configure/
    - docker buildx create --help
    - docker buildx create --name multiarch-builder --driver docker-container --buildkitd-config buildkitd.toml --bootstrap --use builder-context
    - docker buildx inspect multiarch-builder
    - docker buildx use multiarch-builder
  script:
    # 构建多架构镜像
    - set -eo pipefail
    - docker buildx build --platform "${PLATFORMS}" --progress plain --tag "${DOCKER_IMAGE_SERVER}/media-nexus:${CI_COMMIT_TAG}" --push .
  after_script:
    # 登出Docker仓库
    - docker logout $DOCKER_IMAGE_SERVER
    - docker buildx rm multiarch-builder || true
  rules:
    # 仅在标签推送时运行
    - if: $CI_COMMIT_TAG
    # 手动触发
    - when: manual
      allow_failure: true
  tags:
    - shared
